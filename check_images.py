#!/usr/bin/env python3
"""
Check sample images to see if they contain actual content
"""

import os
import glob
import numpy as np
import skimage.io
import matplotlib.pyplot as plt

def check_images(folder_path, num_samples=9):
    print("Checking sample images...")
    
    # Get PNG files
    png_files = glob.glob(os.path.join(folder_path, "*.png"))
    png_files.sort()
    
    if not png_files:
        print("No PNG files found!")
        return
    
    # Check first few images
    fig, axes = plt.subplots(3, 3, figsize=(12, 12))
    axes = axes.flatten()
    
    for i in range(min(num_samples, len(png_files))):
        img = skimage.io.imread(png_files[i])
        if img.ndim == 3:
            img = img[:,:,0]  # Take first channel if RGB
        
        # Calculate statistics
        mean_val = np.mean(img)
        std_val = np.std(img)
        min_val = np.min(img)
        max_val = np.max(img)
        
        # Display image
        axes[i].imshow(img, cmap='gray')
        axes[i].set_title(f"File {i}\nMean: {mean_val:.1f}\nStd: {std_val:.1f}\nRange: {min_val}-{max_val}")
        axes[i].axis('off')
        
        print(f"File {i}: {os.path.basename(png_files[i])}")
        print(f"  Mean: {mean_val:.1f}, Std: {std_val:.1f}, Range: {min_val}-{max_val}")
        
        # Check if image is mostly empty
        if mean_val < 10 and std_val < 5:
            print(f"  ⚠️  WARNING: Image appears mostly empty/dark")
        elif std_val < 1:
            print(f"  ⚠️  WARNING: Image has very low contrast")
        else:
            print(f"  ✅ Image appears to have content")
    
    plt.tight_layout()
    plt.savefig('sample_images.png', dpi=150, bbox_inches='tight')
    print(f"\nSample images saved as 'sample_images.png'")
    plt.show()

def main():
    folder_path = "D:/Stitch/bilinear_stitching_20250915_152529"
    
    if len(os.sys.argv) > 1:
        folder_path = os.sys.argv[1]
    
    if not os.path.exists(folder_path):
        print(f"Folder not found: {folder_path}")
        return
    
    check_images(folder_path)

if __name__ == "__main__":
    main()
