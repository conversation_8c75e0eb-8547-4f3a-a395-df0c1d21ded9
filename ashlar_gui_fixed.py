#!/usr/bin/env python3
"""
ASHLAR GUI - Fixed Version for Bilinear Stitching Files
Author: AI Assistant
Description: User-friendly GUI for stitching microscopy images using ASHLAR
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import sys
import subprocess
import threading
from pathlib import Path
import json
import glob
import re

class AshlarGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("ASHLAR Image Stitching GUI - Fixed Version")
        self.root.geometry("800x700")
        
        # Variables with defaults for your file format
        self.input_folder = tk.StringVar()
        self.output_file = tk.StringVar()
        self.file_pattern = tk.StringVar(value="stitch_bilinear_p{series:03d}_*.png")
        self.overlap = tk.DoubleVar(value=0.2)
        self.width = tk.IntVar(value=9)
        self.height = tk.IntVar(value=3)
        self.pixel_size = tk.DoubleVar(value=1.0)
        self.layout = tk.StringVar(value="snake")
        self.direction = tk.StringVar(value="horizontal")
        self.flip_x = tk.BooleanVar(value=False)
        self.flip_y = tk.BooleanVar(value=True)
        self.max_shift = tk.DoubleVar(value=15.0)
        self.filter_sigma = tk.DoubleVar(value=0.0)
        
        self.create_widgets()
        
    def create_widgets(self):
        # Main frame with scrollbar
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = ttk.Label(main_frame, text="ASHLAR Image Stitching - Bilinear Files", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Input/Output Section
        io_frame = ttk.LabelFrame(main_frame, text="Input/Output Settings", padding=10)
        io_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Input folder
        ttk.Label(io_frame, text="Input Folder:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(io_frame, textvariable=self.input_folder, width=50).grid(row=0, column=1, padx=5, pady=2)
        ttk.Button(io_frame, text="Browse", command=self.browse_input_folder).grid(row=0, column=2, pady=2)
        
        # Output file
        ttk.Label(io_frame, text="Output File:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(io_frame, textvariable=self.output_file, width=50).grid(row=1, column=1, padx=5, pady=2)
        ttk.Button(io_frame, text="Browse", command=self.browse_output_file).grid(row=1, column=2, pady=2)
        
        # File Pattern Section
        pattern_frame = ttk.LabelFrame(main_frame, text="File Pattern Settings", padding=10)
        pattern_frame.pack(fill=tk.X, pady=(0, 10))
        
        # File pattern
        ttk.Label(pattern_frame, text="File Pattern:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(pattern_frame, textvariable=self.file_pattern, width=50).grid(row=0, column=1, columnspan=2, sticky=tk.W, padx=5, pady=2)
        
        # Pattern info
        info_text = ("Pattern for your files: stitch_bilinear_p{series:03d}_*.png\n"
                    "The * matches any characters (like coordinates and timestamp)")
        ttk.Label(pattern_frame, text=info_text, font=("Arial", 8), foreground="blue").grid(row=1, column=1, columnspan=2, sticky=tk.W, padx=5, pady=2)
        
        # Grid Settings Section
        grid_frame = ttk.LabelFrame(main_frame, text="Grid Settings", padding=10)
        grid_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Overlap
        ttk.Label(grid_frame, text="Overlap (0.0-1.0):").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(grid_frame, textvariable=self.overlap, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        # Width and Height
        ttk.Label(grid_frame, text="Width (columns):").grid(row=0, column=2, sticky=tk.W, padx=(20,0), pady=2)
        ttk.Entry(grid_frame, textvariable=self.width, width=10).grid(row=0, column=3, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(grid_frame, text="Height (rows):").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(grid_frame, textvariable=self.height, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        # Pixel size
        ttk.Label(grid_frame, text="Pixel Size (μm):").grid(row=1, column=2, sticky=tk.W, padx=(20,0), pady=2)
        ttk.Entry(grid_frame, textvariable=self.pixel_size, width=10).grid(row=1, column=3, sticky=tk.W, padx=5, pady=2)
        
        # Layout and Direction
        ttk.Label(grid_frame, text="Layout:").grid(row=2, column=0, sticky=tk.W, pady=2)
        layout_combo = ttk.Combobox(grid_frame, textvariable=self.layout, 
                                   values=["raster", "snake"], state="readonly", width=10)
        layout_combo.grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(grid_frame, text="Direction:").grid(row=2, column=2, sticky=tk.W, padx=(20,0), pady=2)
        direction_combo = ttk.Combobox(grid_frame, textvariable=self.direction, 
                                      values=["horizontal", "vertical"], state="readonly", width=10)
        direction_combo.grid(row=2, column=3, sticky=tk.W, padx=5, pady=2)
        
        # Advanced Settings Section
        advanced_frame = ttk.LabelFrame(main_frame, text="Advanced Settings", padding=10)
        advanced_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Flip options
        ttk.Checkbutton(advanced_frame, text="Flip X", variable=self.flip_x).grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Checkbutton(advanced_frame, text="Flip Y (Start from bottom)", variable=self.flip_y).grid(row=0, column=1, sticky=tk.W, padx=20, pady=2)
        
        # Max shift
        ttk.Label(advanced_frame, text="Max Shift (μm):").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(advanced_frame, textvariable=self.max_shift, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        # Filter sigma
        ttk.Label(advanced_frame, text="Filter Sigma:").grid(row=1, column=2, sticky=tk.W, padx=(20,0), pady=2)
        ttk.Entry(advanced_frame, textvariable=self.filter_sigma, width=10).grid(row=1, column=3, sticky=tk.W, padx=5, pady=2)
        
        # Control Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(button_frame, text="Auto-Detect Files", command=self.auto_detect_files).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Preview Command", command=self.preview_command).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Start Stitching", command=self.start_stitching, 
                  style="Accent.TButton").pack(side=tk.RIGHT, padx=5)
        
        # Progress and Log Section
        log_frame = ttk.LabelFrame(main_frame, text="Progress & Log", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Progress bar
        self.progress = ttk.Progressbar(log_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=(0, 10))
        
        # Log text area
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=80)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
    def browse_input_folder(self):
        folder = filedialog.askdirectory(title="Select Input Folder")
        if folder:
            self.input_folder.set(folder)
            # Auto-set output file
            if not self.output_file.get():
                output_path = os.path.join(folder, "stitched_output.ome.tif")
                self.output_file.set(output_path)
            
            # Auto-detect files
            self.auto_detect_files()
    
    def browse_output_file(self):
        file = filedialog.asksaveasfilename(
            title="Save Stitched Image As",
            defaultextension=".ome.tif",
            filetypes=[("OME-TIFF files", "*.ome.tif"), ("TIFF files", "*.tif"), ("All files", "*.*")]
        )
        if file:
            self.output_file.set(file)
    
    def auto_detect_files(self):
        """Auto-detect file pattern and grid dimensions from folder contents"""
        if not self.input_folder.get():
            messagebox.showwarning("Warning", "Please select input folder first")
            return
            
        try:
            folder = self.input_folder.get()
            
            # Get all PNG files
            png_files = glob.glob(os.path.join(folder, "*.png"))
            if not png_files:
                self.log_message("No PNG files found in folder")
                return
            
            # Extract series numbers from files like "stitch_bilinear_p000_..."
            series_numbers = []
            for file in png_files:
                basename = os.path.basename(file)
                # Look for pattern like "p000", "p001", etc.
                match = re.search(r'p(\d+)', basename)
                if match:
                    series_numbers.append(int(match.group(1)))
            
            if not series_numbers:
                self.log_message("Could not detect series numbers in filenames")
                return
            
            series_numbers.sort()
            total_tiles = len(series_numbers)
            
            # Check if series numbers are consecutive starting from 0
            expected_series = list(range(total_tiles))
            if series_numbers != expected_series:
                self.log_message(f"Warning: Series numbers are not consecutive 0-{total_tiles-1}")
                self.log_message(f"Found: {series_numbers}")
            
            # Try to guess grid dimensions
            possible_grids = []
            for w in range(1, total_tiles + 1):
                if total_tiles % w == 0:
                    h = total_tiles // w
                    possible_grids.append((w, h))
            
            # Prefer wider grids (more columns than rows)
            if possible_grids:
                possible_grids.sort(key=lambda x: x[0]/x[1], reverse=True)
                width, height = possible_grids[0]
                self.width.set(width)
                self.height.set(height)
            
            self.log_message(f"✓ Auto-detected: {total_tiles} tiles")
            self.log_message(f"✓ Suggested grid: {self.width.get()}x{self.height.get()}")
            self.log_message(f"✓ Pattern: {self.file_pattern.get()}")
            
            # Show all possible grid options
            if len(possible_grids) > 1:
                grid_options = [f"{w}x{h}" for w, h in possible_grids]
                self.log_message(f"Other possible grids: {', '.join(grid_options)}")
                
        except Exception as e:
            self.log_message(f"Auto-detection failed: {str(e)}")
    
    def build_command(self):
        if not self.input_folder.get():
            raise ValueError("Please select an input folder")
        if not self.output_file.get():
            raise ValueError("Please specify an output file")
        
        # Build command using fileseries reader
        cmd = [
            "ashlar",
            f"fileseries|{self.input_folder.get()}|{self.file_pattern.get()}|{self.overlap.get()}|{self.width.get()}|{self.height.get()}|{self.layout.get()}|{self.direction.get()}|pixel_size={self.pixel_size.get()}",
            "-o", self.output_file.get()
        ]
        
        # Add optional parameters
        if self.flip_x.get():
            cmd.append("--flip-x")
        
        if self.flip_y.get():
            cmd.append("--flip-y")
        
        if self.max_shift.get() != 15.0:
            cmd.extend(["-m", str(self.max_shift.get())])
        
        if self.filter_sigma.get() != 0.0:
            cmd.extend(["--filter-sigma", str(self.filter_sigma.get())])
        
        return cmd
    
    def preview_command(self):
        try:
            cmd = self.build_command()
            command_str = " ".join(f'"{arg}"' if " " in arg else arg for arg in cmd)
            
            # Show command in a dialog
            dialog = tk.Toplevel(self.root)
            dialog.title("Command Preview")
            dialog.geometry("700x300")
            
            ttk.Label(dialog, text="Generated ASHLAR Command:", font=("Arial", 12, "bold")).pack(pady=10)
            
            text_widget = tk.Text(dialog, height=10, wrap=tk.WORD)
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            text_widget.insert(tk.END, command_str)
            text_widget.config(state=tk.DISABLED)
            
            ttk.Button(dialog, text="Close", command=dialog.destroy).pack(pady=10)
            
        except Exception as e:
            messagebox.showerror("Error", f"Error building command: {str(e)}")
    
    def log_message(self, message):
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update()
    
    def start_stitching(self):
        try:
            cmd = self.build_command()
            self.log_message("=" * 50)
            self.log_message("Starting ASHLAR stitching...")
            self.log_message(f"Command: {' '.join(cmd)}")
            self.log_message("=" * 50)
            
            # Start progress bar
            self.progress.start()
            
            # Run in separate thread
            thread = threading.Thread(target=self.run_ashlar, args=(cmd,))
            thread.daemon = True
            thread.start()
            
        except Exception as e:
            messagebox.showerror("Error", f"Error starting stitching: {str(e)}")
    
    def run_ashlar(self, cmd):
        try:
            # Run ASHLAR command
            process = subprocess.Popen(
                cmd, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.STDOUT, 
                universal_newlines=True,
                bufsize=1
            )
            
            # Read output line by line
            for line in process.stdout:
                self.root.after(0, self.log_message, line.strip())
            
            process.wait()
            
            # Stop progress bar
            self.root.after(0, self.progress.stop)
            
            if process.returncode == 0:
                self.root.after(0, self.log_message, "=" * 50)
                self.root.after(0, self.log_message, "✓ Stitching completed successfully!")
                self.root.after(0, self.log_message, f"✓ Output saved to: {self.output_file.get()}")
                self.root.after(0, messagebox.showinfo, "Success", "Image stitching completed successfully!")
            else:
                self.root.after(0, self.log_message, "=" * 50)
                self.root.after(0, self.log_message, f"✗ Stitching failed with return code {process.returncode}")
                self.root.after(0, messagebox.showerror, "Error", "Stitching process failed. Check the log for details.")
                
        except Exception as e:
            self.root.after(0, self.progress.stop)
            self.root.after(0, self.log_message, f"✗ Error: {str(e)}")
            self.root.after(0, messagebox.showerror, "Error", f"Error running ASHLAR: {str(e)}")

def main():
    # Check if ASHLAR is available
    try:
        subprocess.run(["ashlar", "--version"], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        messagebox.showerror(
            "ASHLAR Not Found", 
            "ASHLAR is not installed or not in PATH.\n\n"
            "Please install ASHLAR first:\n"
            "pip install ashlar\n\n"
            "Or make sure it's in your system PATH."
        )
        return
    
    root = tk.Tk()
    app = AshlarGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
