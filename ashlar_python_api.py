#!/usr/bin/env python3
"""
ASHLAR Python API GUI - Direct Python API usage instead of command line
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import threading
import glob
import re
import sys

class PythonAPIAshlarGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("ASHLAR Python API GUI")
        self.root.geometry("700x600")
        
        # Variables
        self.input_folder = tk.StringVar()
        self.output_file = tk.StringVar()
        self.file_pattern = tk.StringVar(value="stitch_bilinear_p{series:03d}_*.png")
        self.overlap = tk.DoubleVar(value=0.2)
        self.width = tk.IntVar(value=6)
        self.height = tk.IntVar(value=6)
        self.pixel_size = tk.DoubleVar(value=1.0)
        self.layout = tk.StringVar(value="snake")
        self.direction = tk.StringVar(value="horizontal")
        self.flip_x = tk.BooleanVar(value=False)
        self.flip_y = tk.BooleanVar(value=True)
        
        self.create_widgets()
        
    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = ttk.Label(main_frame, text="ASHLAR Python API GUI", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Input/Output Section
        io_frame = ttk.LabelFrame(main_frame, text="Input/Output", padding=10)
        io_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Input folder
        ttk.Label(io_frame, text="Input Folder:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(io_frame, textvariable=self.input_folder, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(io_frame, text="Browse", command=self.browse_input_folder).grid(row=0, column=2, pady=5)
        
        # Output file
        ttk.Label(io_frame, text="Output File:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(io_frame, textvariable=self.output_file, width=50).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(io_frame, text="Browse", command=self.browse_output_file).grid(row=1, column=2, pady=5)
        
        # Settings Section
        settings_frame = ttk.LabelFrame(main_frame, text="Stitching Settings", padding=10)
        settings_frame.pack(fill=tk.X, pady=(0, 10))
        
        # File pattern
        ttk.Label(settings_frame, text="File Pattern:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(settings_frame, textvariable=self.file_pattern, width=40).grid(row=0, column=1, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # Grid settings
        ttk.Label(settings_frame, text="Grid Width:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(settings_frame, textvariable=self.width, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(settings_frame, text="Grid Height:").grid(row=1, column=2, sticky=tk.W, padx=(20,0), pady=5)
        ttk.Entry(settings_frame, textvariable=self.height, width=10).grid(row=1, column=3, sticky=tk.W, padx=5, pady=5)
        
        # Overlap and pixel size
        ttk.Label(settings_frame, text="Overlap (0.0-1.0):").grid(row=2, column=0, sticky=tk.W, pady=5)
        ttk.Entry(settings_frame, textvariable=self.overlap, width=10).grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(settings_frame, text="Pixel Size (μm):").grid(row=2, column=2, sticky=tk.W, padx=(20,0), pady=5)
        ttk.Entry(settings_frame, textvariable=self.pixel_size, width=10).grid(row=2, column=3, sticky=tk.W, padx=5, pady=5)
        
        # Layout and direction
        ttk.Label(settings_frame, text="Layout:").grid(row=3, column=0, sticky=tk.W, pady=5)
        layout_combo = ttk.Combobox(settings_frame, textvariable=self.layout, 
                                   values=["raster", "snake"], state="readonly", width=10)
        layout_combo.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(settings_frame, text="Direction:").grid(row=3, column=2, sticky=tk.W, padx=(20,0), pady=5)
        direction_combo = ttk.Combobox(settings_frame, textvariable=self.direction, 
                                      values=["horizontal", "vertical"], state="readonly", width=10)
        direction_combo.grid(row=3, column=3, sticky=tk.W, padx=5, pady=5)
        
        # Flip options
        flip_frame = ttk.Frame(settings_frame)
        flip_frame.grid(row=4, column=0, columnspan=4, sticky=tk.W, pady=10)
        ttk.Checkbutton(flip_frame, text="Flip X", variable=self.flip_x).pack(side=tk.LEFT, padx=5)
        ttk.Checkbutton(flip_frame, text="Flip Y (start from bottom)", variable=self.flip_y).pack(side=tk.LEFT, padx=20)
        
        # Control Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(button_frame, text="Auto-Detect Files", command=self.auto_detect_files).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Start Stitching", command=self.start_stitching).pack(side=tk.RIGHT, padx=5)
        
        # Progress and Log Section
        log_frame = ttk.LabelFrame(main_frame, text="Progress & Log", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Progress bar
        self.progress = ttk.Progressbar(log_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=(0, 10))
        
        # Log text area
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, width=80)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
    def browse_input_folder(self):
        folder = filedialog.askdirectory(title="Select Input Folder")
        if folder:
            self.input_folder.set(folder)
            if not self.output_file.get():
                output_path = os.path.join(folder, "stitched_output.ome.tif")
                self.output_file.set(output_path)
            self.auto_detect_files()
    
    def browse_output_file(self):
        file = filedialog.asksaveasfilename(
            title="Save Stitched Image As",
            defaultextension=".ome.tif",
            filetypes=[("OME-TIFF files", "*.ome.tif"), ("TIFF files", "*.tif")]
        )
        if file:
            self.output_file.set(file)
    
    def auto_detect_files(self):
        if not self.input_folder.get():
            return
            
        try:
            folder = self.input_folder.get()
            png_files = glob.glob(os.path.join(folder, "*.png"))
            
            if not png_files:
                self.log_message("No PNG files found")
                return
            
            # Extract series numbers
            series_numbers = []
            sample_file = os.path.basename(png_files[0])
            
            for file in png_files:
                basename = os.path.basename(file)
                match = re.search(r'p(\d+)', basename)
                if match:
                    series_numbers.append(int(match.group(1)))
            
            if not series_numbers:
                self.log_message("Could not detect series numbers")
                return
            
            series_numbers.sort()
            total_tiles = len(series_numbers)
            
            # Update pattern
            if "stitch_bilinear" in sample_file:
                self.file_pattern.set("stitch_bilinear_p{series:03d}_*.png")
            
            # Suggest grid dimensions for common cases
            if total_tiles == 36:
                self.width.set(6)
                self.height.set(6)
            elif total_tiles == 27:
                self.width.set(9)
                self.height.set(3)
            elif total_tiles == 24:
                self.width.set(6)
                self.height.set(4)
            elif total_tiles == 20:
                self.width.set(5)
                self.height.set(4)
            elif total_tiles == 16:
                self.width.set(4)
                self.height.set(4)
            elif total_tiles == 12:
                self.width.set(4)
                self.height.set(3)
            elif total_tiles == 9:
                self.width.set(3)
                self.height.set(3)
            else:
                # Fallback: find factors
                for w in range(int(total_tiles**0.5), 0, -1):
                    if total_tiles % w == 0:
                        h = total_tiles // w
                        self.width.set(w)
                        self.height.set(h)
                        break
            
            self.log_message(f"✓ Detected {total_tiles} tiles")
            self.log_message(f"✓ Pattern: {self.file_pattern.get()}")
            self.log_message(f"✓ Suggested grid: {self.width.get()}x{self.height.get()}")
            self.log_message(f"✓ Sample file: {sample_file}")
            
        except Exception as e:
            self.log_message(f"Auto-detection error: {str(e)}")
    
    def log_message(self, message):
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update()
    
    def start_stitching(self):
        try:
            if not self.input_folder.get():
                raise ValueError("Please select input folder")
            if not self.output_file.get():
                raise ValueError("Please specify output file")
            
            self.log_message("=" * 50)
            self.log_message("Starting ASHLAR stitching using Python API...")
            self.log_message("=" * 50)
            
            self.progress.start()
            
            thread = threading.Thread(target=self.run_ashlar_python)
            thread.daemon = True
            thread.start()
            
        except Exception as e:
            messagebox.showerror("Error", f"Error: {str(e)}")
    
    def run_ashlar_python(self):
        try:
            # Import ASHLAR modules
            from ashlar.fileseries import FileSeriesReader
            from ashlar import reg
            
            self.root.after(0, self.log_message, "✓ ASHLAR modules imported successfully")
            
            # Create FileSeriesReader
            reader = FileSeriesReader(
                path=self.input_folder.get(),
                pattern=self.file_pattern.get(),
                overlap=self.overlap.get(),
                width=self.width.get(),
                height=self.height.get(),
                layout=self.layout.get(),
                direction=self.direction.get(),
                pixel_size=self.pixel_size.get()
            )
            
            self.root.after(0, self.log_message, f"✓ FileSeriesReader created")
            self.root.after(0, self.log_message, f"  - Found {reader.metadata.num_images} images")
            self.root.after(0, self.log_message, f"  - Image size: {reader.metadata.size}")
            
            # Apply axis flips if needed
            if self.flip_x.get() or self.flip_y.get():
                metadata = reader.metadata
                positions = metadata.positions
                sx = -1 if self.flip_x.get() else 1
                sy = -1 if self.flip_y.get() else 1
                metadata._positions = positions * [sy, sx]
                self.root.after(0, self.log_message, f"✓ Applied axis flips: X={self.flip_x.get()}, Y={self.flip_y.get()}")
            
            # Create EdgeAligner
            self.root.after(0, self.log_message, "Starting edge alignment...")
            edge_aligner = reg.EdgeAligner(reader, verbose=True)
            edge_aligner.run()
            
            self.root.after(0, self.log_message, "✓ Edge alignment completed")
            self.root.after(0, self.log_message, f"  - Mosaic shape: {edge_aligner.mosaic_shape}")
            
            # Create Mosaic
            self.root.after(0, self.log_message, "Creating mosaic...")
            mosaic = reg.Mosaic(edge_aligner, edge_aligner.mosaic_shape, verbose=True)
            
            # Determine output format
            output_path = self.output_file.get()
            if output_path.endswith('.ome.tif'):
                # Write as pyramidal OME-TIFF
                self.root.after(0, self.log_message, "Writing pyramidal OME-TIFF...")
                writer = reg.PyramidWriter([mosaic], output_path, verbose=True)
                writer.run()
            else:
                # Write as regular TIFF
                self.root.after(0, self.log_message, "Writing TIFF...")
                writer = reg.TiffListWriter([mosaic], output_path, verbose=True)
                writer.run()
            
            self.root.after(0, self.progress.stop)
            self.root.after(0, self.log_message, "=" * 50)
            self.root.after(0, self.log_message, "✓ Stitching completed successfully!")
            self.root.after(0, self.log_message, f"✓ Output saved to: {output_path}")
            self.root.after(0, messagebox.showinfo, "Success", "Stitching completed successfully!")
            
        except Exception as e:
            self.root.after(0, self.progress.stop)
            self.root.after(0, self.log_message, f"✗ Error: {str(e)}")
            self.root.after(0, messagebox.showerror, "Error", f"Error during stitching: {str(e)}")

def main():
    try:
        import ashlar
        print(f"ASHLAR version: {ashlar.__version__}")
    except ImportError:
        messagebox.showerror(
            "ASHLAR Not Found", 
            "ASHLAR not found. Please install:\npip install ashlar"
        )
        return
    
    root = tk.Tk()
    app = PythonAPIAshlarGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
