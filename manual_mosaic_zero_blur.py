#!/usr/bin/env python3
"""
Create mosaic with ZERO BLUR - edge-only blending
Preserves 100% image sharpness while eliminating seams
"""

import os
import glob
import numpy as np
import skimage.io
import tifffile

def create_zero_blur_weight(shape, edge_pixels=3):
    """Create weight that only blends at very edge - zero blur in main area"""
    h, w = shape
    y, x = np.ogrid[:h, :w]
    
    # Distance from edges
    dist_top = y
    dist_bottom = h - 1 - y
    dist_left = x
    dist_right = w - 1 - x
    
    # Minimum distance to any edge
    dist_to_edge = np.minimum(np.minimum(dist_top, dist_bottom), 
                             np.minimum(dist_left, dist_right))
    
    # ZERO BLUR: Full weight everywhere except very edge
    weight = np.ones((h, w), dtype=np.float64)
    
    # Only reduce weight in tiny edge region (2-3 pixels)
    edge_mask = dist_to_edge < edge_pixels
    weight[edge_mask] = dist_to_edge[edge_mask] / edge_pixels
    
    return weight

def create_priority_mask(shape, tile_index, total_tiles):
    """Create priority mask - center tiles get higher priority"""
    h, w = shape
    
    # Simple priority: later tiles (more central) get higher priority
    priority = (tile_index + 1) / total_tiles
    
    # Create mask with priority value
    mask = np.full((h, w), priority, dtype=np.float64)
    
    return mask

def create_zero_blur_mosaic(folder_path):
    print("Creating ZERO BLUR mosaic...")
    print("🎯 100% sharpness preservation with edge-only blending")
    
    # Get PNG files
    png_files = glob.glob(os.path.join(folder_path, "*.png"))
    png_files.sort()
    
    if not png_files:
        print("No PNG files found!")
        return
    
    # Read first image to get dimensions and check if color
    first_img = skimage.io.imread(png_files[0])
    is_color = first_img.ndim == 3
    
    if is_color:
        tile_height, tile_width, channels = first_img.shape
        print(f"✅ COLOR images detected: {channels} channels")
    else:
        tile_height, tile_width = first_img.shape
        channels = 1
        print("⚠️ Grayscale images detected")
    
    num_images = len(png_files)
    print(f"Tile dimensions: {tile_width} x {tile_height}")
    print(f"Number of tiles: {num_images}")
    
    # Parameters
    grid_width = 9
    grid_height = 4
    overlap = 0.2  # Keep original overlap but use different blending
    
    # Calculate positions
    positions = []
    for i in range(num_images):
        row = i // grid_width
        col = i % grid_width
        
        # Snake pattern
        if row % 2 == 1:
            col = grid_width - 1 - col
        
        pos_y = row * tile_height * (1 - overlap)
        pos_x = col * tile_width * (1 - overlap)
        positions.append([pos_y, pos_x])
    
    positions = np.array(positions)
    
    # Apply flip Y and make positive
    positions = positions * [-1, 1]
    min_y = positions[:,0].min()
    min_x = positions[:,1].min()
    
    if min_y < 0:
        positions[:,0] -= min_y
    if min_x < 0:
        positions[:,1] -= min_x
    
    # Calculate mosaic size
    max_y = int(positions[:,0].max() + tile_height)
    max_x = int(positions[:,1].max() + tile_width)
    
    print(f"Mosaic size: {max_x} x {max_y}")
    
    # Create mosaic arrays
    if is_color:
        mosaic = np.zeros((max_y, max_x, channels), dtype=np.float64)
        priority_map = np.zeros((max_y, max_x), dtype=np.float64)
    else:
        mosaic = np.zeros((max_y, max_x), dtype=np.float64)
        priority_map = np.zeros((max_y, max_x), dtype=np.float64)
    
    # EDGE-ONLY blending - only 2-3 pixels at edge
    edge_blend_pixels = 3
    print(f"Edge-only blending: {edge_blend_pixels} pixels (ZERO blur in main area)")
    
    print("Placing tiles with ZERO BLUR method...")
    
    # Method: Use priority-based placement with edge blending
    for i, (y, x) in enumerate(positions):
        if i >= len(png_files):
            break
        
        # Load image
        img = skimage.io.imread(png_files[i])
        img_float = img.astype(np.float64)
        
        # Calculate placement region
        y_start = int(y)
        y_end = y_start + tile_height
        x_start = int(x)
        x_end = x_start + tile_width
        
        # Ensure we don't go out of bounds
        y_end = min(y_end, max_y)
        x_end = min(x_end, max_x)
        
        # Get actual tile size
        actual_h = y_end - y_start
        actual_w = x_end - x_start
        
        if actual_h <= 0 or actual_w <= 0:
            continue
        
        # Create weight and priority for this tile
        tile_weight = create_zero_blur_weight((actual_h, actual_w), edge_blend_pixels)
        tile_priority = create_priority_mask((actual_h, actual_w), i, num_images)
        
        # Get regions
        mosaic_region_y = slice(y_start, y_end)
        mosaic_region_x = slice(x_start, x_end)
        
        if is_color:
            img_region = img_float[:actual_h, :actual_w, :]
            existing_priority = priority_map[mosaic_region_y, mosaic_region_x]
            
            # Only update pixels where this tile has higher priority OR in edge blend zone
            update_mask = (tile_priority > existing_priority) | (tile_weight < 1.0)
            
            for c in range(channels):
                existing_mosaic = mosaic[mosaic_region_y, mosaic_region_x, c]
                new_values = img_region[:,:,c]
                
                # In edge zones: blend, elsewhere: use higher priority
                blend_factor = tile_weight
                final_values = np.where(update_mask,
                                      blend_factor * new_values + (1 - blend_factor) * existing_mosaic,
                                      existing_mosaic)
                
                # But if this tile has higher priority, use it directly (ZERO blur)
                priority_mask = tile_priority > existing_priority
                final_values = np.where(priority_mask, new_values, final_values)
                
                mosaic[mosaic_region_y, mosaic_region_x, c] = final_values
        else:
            img_region = img_float[:actual_h, :actual_w]
            existing_priority = priority_map[mosaic_region_y, mosaic_region_x]
            
            update_mask = (tile_priority > existing_priority) | (tile_weight < 1.0)
            
            existing_mosaic = mosaic[mosaic_region_y, mosaic_region_x]
            new_values = img_region
            
            blend_factor = tile_weight
            final_values = np.where(update_mask,
                                  blend_factor * new_values + (1 - blend_factor) * existing_mosaic,
                                  existing_mosaic)
            
            priority_mask = tile_priority > existing_priority
            final_values = np.where(priority_mask, new_values, final_values)
            
            mosaic[mosaic_region_y, mosaic_region_x] = final_values
        
        # Update priority map
        priority_map[mosaic_region_y, mosaic_region_x] = np.maximum(
            priority_map[mosaic_region_y, mosaic_region_x], tile_priority)
        
        if i % 10 == 0:
            print(f"Placed tile {i}/{num_images}")
    
    # Convert back to original dtype
    mosaic = mosaic.astype(first_img.dtype)
    
    # Save results
    print("Saving ZERO BLUR results...")
    
    # Save as PNG
    png_output = os.path.join(folder_path, "final_mosaic_ZERO_BLUR.png")
    skimage.io.imsave(png_output, mosaic)
    print(f"PNG mosaic saved: {png_output}")
    
    # Save as TIFF
    tiff_output = os.path.join(folder_path, "final_mosaic_ZERO_BLUR.tif")
    tifffile.imwrite(tiff_output, mosaic)
    print(f"TIFF mosaic saved: {tiff_output}")
    
    # Save as OME-TIFF
    ome_output = os.path.join(folder_path, "final_mosaic_ZERO_BLUR.ome.tif")
    tifffile.imwrite(ome_output, mosaic, 
                    metadata={'axes': 'YX', 'PhysicalSizeX': 1.0, 'PhysicalSizeY': 1.0})
    print(f"OME-TIFF mosaic saved: {ome_output}")
    
    # Statistics
    print(f"\nZERO BLUR mosaic statistics:")
    print(f"Size: {mosaic.shape[1]} x {mosaic.shape[0]} pixels")
    print(f"Mean intensity: {np.mean(mosaic):.1f}")
    print(f"Std intensity: {np.std(mosaic):.1f}")
    print(f"Edge blend zone: {edge_blend_pixels} pixels only")
    print(f"Main area blur: 0% (ZERO BLUR)")
    
    return mosaic

def main():
    folder_path = "D:/Stitch/bilinear_stitching_20250915_152529"
    
    if len(os.sys.argv) > 1:
        folder_path = os.sys.argv[1]
    
    if not os.path.exists(folder_path):
        print(f"Folder not found: {folder_path}")
        return
    
    mosaic = create_zero_blur_mosaic(folder_path)
    
    print("\n" + "=" * 50)
    print("🎯 ZERO BLUR MOSAIC CREATED!")
    print("=" * 50)
    print("Files created:")
    print("- final_mosaic_ZERO_BLUR.png")
    print("- final_mosaic_ZERO_BLUR.tif") 
    print("- final_mosaic_ZERO_BLUR.ome.tif")
    print("\n✅ FEATURES:")
    print("🎯 ZERO blur in main image areas")
    print("🔧 Edge-only blending (3 pixels)")
    print("📊 Priority-based tile placement")
    print("🔬 100% detail preservation")

if __name__ == "__main__":
    main()
