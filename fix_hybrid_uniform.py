#!/usr/bin/env python3
"""
Fixed Hybrid Stitching - Handle non-uniform image sizes
"""

import os
import glob
import numpy as np
import skimage.io
import tifffile
from collections import Counter

def analyze_image_sizes(folder_path):
    """Analyze image sizes and find the most common size"""
    print("🔍 Analyzing image sizes...")
    
    png_files = glob.glob(os.path.join(folder_path, "*.png"))
    png_files.sort()
    
    sizes = []
    for i, img_path in enumerate(png_files):
        img = skimage.io.imread(img_path)
        size = (img.shape[0], img.shape[1])  # (height, width)
        sizes.append(size)
        
        if i < 10:  # Show first 10
            print(f"Image {i}: {os.path.basename(img_path)} -> {img.shape}")
    
    # Find most common size
    size_counts = Counter(sizes)
    most_common_size, count = size_counts.most_common(1)[0]
    
    print(f"\n📊 Size Analysis:")
    for size, count in size_counts.most_common():
        print(f"  {size[1]}x{size[0]} pixels: {count} images")
    
    print(f"\n✅ Most common size: {most_common_size[1]}x{most_common_size[0]} ({count} images)")
    
    # Filter files with correct size
    correct_files = []
    for i, img_path in enumerate(png_files):
        if sizes[i] == most_common_size:
            correct_files.append(img_path)
        else:
            print(f"⚠️  Skipping {os.path.basename(img_path)}: wrong size {sizes[i]}")
    
    print(f"✅ Using {len(correct_files)} images with correct size")
    
    return correct_files, most_common_size

def create_fixed_hybrid_mosaic(folder_path):
    print("🔧 FIXED HYBRID STITCHING")
    print("=" * 50)
    
    # Analyze and filter images
    correct_files, (tile_height, tile_width) = analyze_image_sizes(folder_path)
    
    if len(correct_files) < 4:
        print("❌ Not enough images with consistent size!")
        return
    
    print(f"\n📐 Using tile size: {tile_width} x {tile_height}")
    
    # Load images
    print("📁 Loading uniform images...")
    images = []
    for i, img_path in enumerate(correct_files):
        img = skimage.io.imread(img_path)
        images.append(img)
        if i % 10 == 0:
            print(f"Loaded {i+1}/{len(correct_files)} images")
    
    # Check properties
    first_img = images[0]
    is_color = first_img.ndim == 3
    
    if is_color:
        channels = first_img.shape[2]
        print(f"✅ COLOR images: {tile_width}x{tile_height}, {channels} channels")
    else:
        channels = 1
        print(f"✅ GRAYSCALE images: {tile_width}x{tile_height}")
    
    num_images = len(images)
    print(f"Total uniform images: {num_images}")
    
    # Determine grid size based on actual image count
    if num_images == 36:
        grid_width, grid_height = 9, 4
    elif num_images == 35:
        grid_width, grid_height = 7, 5  # or adjust as needed
    elif num_images >= 30:
        grid_width = int(np.sqrt(num_images * 2))
        grid_height = int(np.ceil(num_images / grid_width))
    else:
        grid_width = int(np.ceil(np.sqrt(num_images)))
        grid_height = int(np.ceil(num_images / grid_width))
    
    print(f"Grid layout: {grid_width} x {grid_height} (for {num_images} images)")
    
    # Parameters
    overlap = 0.2
    
    # Calculate positions
    print("📐 Calculating positions...")
    positions = []
    for i in range(num_images):
        row = i // grid_width
        col = i % grid_width
        
        # Snake pattern
        if row % 2 == 1:
            col = grid_width - 1 - col
        
        pos_y = row * tile_height * (1 - overlap)
        pos_x = col * tile_width * (1 - overlap)
        positions.append([pos_y, pos_x])
    
    positions = np.array(positions)
    
    # Apply flip Y and make positive
    positions = positions * [-1, 1]
    min_y = positions[:,0].min()
    min_x = positions[:,1].min()
    
    if min_y < 0:
        positions[:,0] -= min_y
    if min_x < 0:
        positions[:,1] -= min_x
    
    # Calculate mosaic size
    max_y = int(positions[:,0].max() + tile_height)
    max_x = int(positions[:,1].max() + tile_width)
    
    print(f"Mosaic size: {max_x} x {max_y}")
    
    # Create simple blending weight
    def create_simple_weight(shape, blend_pixels=50):
        h, w = shape
        weight = np.ones((h, w), dtype=np.float64)
        
        # Feather edges
        for i in range(blend_pixels):
            alpha = i / blend_pixels
            # Top and bottom
            if i < h:
                weight[i, :] *= alpha
                weight[-(i+1), :] *= alpha
            # Left and right
            if i < w:
                weight[:, i] *= alpha
                weight[:, -(i+1)] *= alpha
        
        return weight
    
    # Create mosaic with simple blending
    print("🎨 Creating mosaic with simple blending...")
    
    if is_color:
        mosaic = np.zeros((max_y, max_x, channels), dtype=np.float64)
        weight_sum = np.zeros((max_y, max_x), dtype=np.float64)
    else:
        mosaic = np.zeros((max_y, max_x), dtype=np.float64)
        weight_sum = np.zeros((max_y, max_x), dtype=np.float64)
    
    # Create weight template
    tile_weight = create_simple_weight((tile_height, tile_width), blend_pixels=30)
    
    # Place tiles
    for i, (y, x) in enumerate(positions):
        if i >= len(images):
            break
        
        img = images[i].astype(np.float64)
        
        # Calculate placement region
        y_start = int(y)
        y_end = y_start + tile_height
        x_start = int(x)
        x_end = x_start + tile_width
        
        # Ensure bounds
        y_end = min(y_end, max_y)
        x_end = min(x_end, max_x)
        
        actual_h = y_end - y_start
        actual_w = x_end - x_start
        
        if actual_h <= 0 or actual_w <= 0:
            continue
        
        # Get regions
        if is_color:
            img_region = img[:actual_h, :actual_w, :]
            weight_region = tile_weight[:actual_h, :actual_w]
            
            # Blend each channel
            for c in range(channels):
                mosaic[y_start:y_end, x_start:x_end, c] += img_region[:,:,c] * weight_region
        else:
            img_region = img[:actual_h, :actual_w]
            weight_region = tile_weight[:actual_h, :actual_w]
            mosaic[y_start:y_end, x_start:x_end] += img_region * weight_region
        
        weight_sum[y_start:y_end, x_start:x_end] += weight_region
        
        if i % 5 == 0:
            print(f"Placed tile {i+1}/{num_images}")
    
    # Normalize
    print("🔧 Normalizing...")
    weight_sum[weight_sum == 0] = 1
    
    if is_color:
        for c in range(channels):
            mosaic[:,:,c] /= weight_sum
    else:
        mosaic /= weight_sum
    
    # Convert to uint8
    mosaic = np.clip(mosaic, 0, 255).astype(np.uint8)
    
    # Check result
    print(f"\n📊 Final mosaic statistics:")
    print(f"Shape: {mosaic.shape}")
    print(f"Min: {mosaic.min()}, Max: {mosaic.max()}")
    print(f"Mean: {mosaic.mean():.1f}")
    print(f"Non-zero pixels: {np.count_nonzero(mosaic):,}")
    print(f"Coverage: {np.count_nonzero(mosaic) / mosaic.size * 100:.1f}%")
    
    # Save results
    print("💾 Saving results...")
    
    # PNG
    png_output = os.path.join(folder_path, "FIXED_hybrid_mosaic.png")
    skimage.io.imsave(png_output, mosaic)
    print(f"PNG saved: {png_output}")
    
    # TIFF
    tiff_output = os.path.join(folder_path, "FIXED_hybrid_mosaic.tif")
    tifffile.imwrite(tiff_output, mosaic)
    print(f"TIFF saved: {tiff_output}")
    
    # File size check
    file_size_mb = os.path.getsize(png_output) / (1024 * 1024)
    print(f"File size: {file_size_mb:.1f} MB")
    
    if file_size_mb < 1:
        print("⚠️  WARNING: File size too small, mosaic might be mostly empty")
    else:
        print("✅ File size looks good")
    
    return mosaic

def main():
    folder_path = "D:/Stitch/bilinear_stitching_20250915_152529"
    
    if len(os.sys.argv) > 1:
        folder_path = os.sys.argv[1]
    
    if not os.path.exists(folder_path):
        print(f"Folder not found: {folder_path}")
        return
    
    try:
        result = create_fixed_hybrid_mosaic(folder_path)
        
        print("\n" + "=" * 50)
        print("🎉 FIXED HYBRID STITCHING COMPLETED!")
        print("=" * 50)
        print("✅ Filtered out non-uniform images")
        print("✅ Used consistent tile sizes")
        print("✅ Simple but effective blending")
        print("✅ Should have proper file size now")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
