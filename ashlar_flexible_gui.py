#!/usr/bin/env python3
"""
ASHLAR Flexible GUI - Handles any file naming pattern
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import threading
import glob
import re

class FlexibleAshlarGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("ASHLAR Flexible GUI")
        self.root.geometry("800x700")
        
        # Variables
        self.input_folder = tk.StringVar()
        self.output_file = tk.StringVar()
        self.overlap = tk.DoubleVar(value=0.2)
        self.width = tk.IntVar(value=6)
        self.height = tk.IntVar(value=6)
        self.pixel_size = tk.DoubleVar(value=1.0)
        self.layout = tk.StringVar(value="snake")
        self.direction = tk.StringVar(value="horizontal")
        self.flip_x = tk.BooleanVar(value=False)
        self.flip_y = tk.BooleanVar(value=True)
        
        # File list
        self.file_list = []
        
        self.create_widgets()
        
    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = ttk.Label(main_frame, text="ASHLAR Flexible GUI", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Input/Output Section
        io_frame = ttk.LabelFrame(main_frame, text="Input/Output", padding=10)
        io_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Input folder
        ttk.Label(io_frame, text="Input Folder:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(io_frame, textvariable=self.input_folder, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(io_frame, text="Browse", command=self.browse_input_folder).grid(row=0, column=2, pady=5)
        
        # Output file
        ttk.Label(io_frame, text="Output File:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(io_frame, textvariable=self.output_file, width=50).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(io_frame, text="Browse", command=self.browse_output_file).grid(row=1, column=2, pady=5)
        
        # File List Section
        files_frame = ttk.LabelFrame(main_frame, text="Detected Files", padding=10)
        files_frame.pack(fill=tk.X, pady=(0, 10))
        
        # File listbox with scrollbar
        list_frame = ttk.Frame(files_frame)
        list_frame.pack(fill=tk.X)
        
        self.file_listbox = tk.Listbox(list_frame, height=6)
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_listbox.yview)
        self.file_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.file_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Settings Section
        settings_frame = ttk.LabelFrame(main_frame, text="Stitching Settings", padding=10)
        settings_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Grid settings
        ttk.Label(settings_frame, text="Grid Width:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(settings_frame, textvariable=self.width, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(settings_frame, text="Grid Height:").grid(row=0, column=2, sticky=tk.W, padx=(20,0), pady=5)
        ttk.Entry(settings_frame, textvariable=self.height, width=10).grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        
        # Overlap and pixel size
        ttk.Label(settings_frame, text="Overlap (0.0-1.0):").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(settings_frame, textvariable=self.overlap, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(settings_frame, text="Pixel Size (μm):").grid(row=1, column=2, sticky=tk.W, padx=(20,0), pady=5)
        ttk.Entry(settings_frame, textvariable=self.pixel_size, width=10).grid(row=1, column=3, sticky=tk.W, padx=5, pady=5)
        
        # Layout and direction
        ttk.Label(settings_frame, text="Layout:").grid(row=2, column=0, sticky=tk.W, pady=5)
        layout_combo = ttk.Combobox(settings_frame, textvariable=self.layout, 
                                   values=["raster", "snake"], state="readonly", width=10)
        layout_combo.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(settings_frame, text="Direction:").grid(row=2, column=2, sticky=tk.W, padx=(20,0), pady=5)
        direction_combo = ttk.Combobox(settings_frame, textvariable=self.direction, 
                                      values=["horizontal", "vertical"], state="readonly", width=10)
        direction_combo.grid(row=2, column=3, sticky=tk.W, padx=5, pady=5)
        
        # Flip options
        flip_frame = ttk.Frame(settings_frame)
        flip_frame.grid(row=3, column=0, columnspan=4, sticky=tk.W, pady=10)
        ttk.Checkbutton(flip_frame, text="Flip X", variable=self.flip_x).pack(side=tk.LEFT, padx=5)
        ttk.Checkbutton(flip_frame, text="Flip Y (start from bottom)", variable=self.flip_y).pack(side=tk.LEFT, padx=20)
        
        # Control Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(button_frame, text="Refresh Files", command=self.refresh_files).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Start Stitching", command=self.start_stitching).pack(side=tk.RIGHT, padx=5)
        
        # Progress and Log Section
        log_frame = ttk.LabelFrame(main_frame, text="Progress & Log", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Progress bar
        self.progress = ttk.Progressbar(log_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=(0, 10))
        
        # Log text area
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=80)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
    def browse_input_folder(self):
        folder = filedialog.askdirectory(title="Select Input Folder")
        if folder:
            self.input_folder.set(folder)
            if not self.output_file.get():
                output_path = os.path.join(folder, "stitched_output.ome.tif")
                self.output_file.set(output_path)
            self.refresh_files()
    
    def browse_output_file(self):
        file = filedialog.asksaveasfilename(
            title="Save Stitched Image As",
            defaultextension=".ome.tif",
            filetypes=[("OME-TIFF files", "*.ome.tif"), ("TIFF files", "*.tif")]
        )
        if file:
            self.output_file.set(file)
    
    def refresh_files(self):
        if not self.input_folder.get():
            return
            
        try:
            folder = self.input_folder.get()
            
            # Get all image files
            extensions = ['*.png', '*.tif', '*.tiff', '*.jpg', '*.jpeg']
            all_files = []
            for ext in extensions:
                all_files.extend(glob.glob(os.path.join(folder, ext)))
                all_files.extend(glob.glob(os.path.join(folder, ext.upper())))
            
            if not all_files:
                self.log_message("No image files found")
                return
            
            # Sort files naturally
            all_files.sort()
            self.file_list = all_files
            
            # Update listbox
            self.file_listbox.delete(0, tk.END)
            for file in all_files:
                self.file_listbox.insert(tk.END, os.path.basename(file))
            
            total_tiles = len(all_files)
            
            # Suggest grid dimensions
            if total_tiles == 36:
                self.width.set(6)
                self.height.set(6)
            elif total_tiles == 27:
                self.width.set(9)
                self.height.set(3)
            elif total_tiles == 24:
                self.width.set(6)
                self.height.set(4)
            elif total_tiles == 20:
                self.width.set(5)
                self.height.set(4)
            elif total_tiles == 16:
                self.width.set(4)
                self.height.set(4)
            elif total_tiles == 12:
                self.width.set(4)
                self.height.set(3)
            elif total_tiles == 9:
                self.width.set(3)
                self.height.set(3)
            else:
                # Find best grid
                best_ratio = float('inf')
                best_w, best_h = 1, total_tiles
                for w in range(1, int(total_tiles**0.5) + 2):
                    if total_tiles % w == 0:
                        h = total_tiles // w
                        ratio = max(w/h, h/w)  # Prefer square-ish grids
                        if ratio < best_ratio:
                            best_ratio = ratio
                            best_w, best_h = w, h
                self.width.set(best_w)
                self.height.set(best_h)
            
            self.log_message(f"✓ Found {total_tiles} image files")
            self.log_message(f"✓ Suggested grid: {self.width.get()}x{self.height.get()}")
            self.log_message(f"✓ Files will be processed in alphabetical order")
            
        except Exception as e:
            self.log_message(f"Error refreshing files: {str(e)}")
    
    def log_message(self, message):
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update()
    
    def start_stitching(self):
        try:
            if not self.file_list:
                raise ValueError("No files found. Please select input folder and refresh files.")
            if not self.output_file.get():
                raise ValueError("Please specify output file")
            
            self.log_message("=" * 50)
            self.log_message("Starting ASHLAR stitching...")
            self.log_message("=" * 50)
            
            self.progress.start()
            
            thread = threading.Thread(target=self.run_ashlar_python)
            thread.daemon = True
            thread.start()
            
        except Exception as e:
            messagebox.showerror("Error", f"Error: {str(e)}")
    
    def run_ashlar_python(self):
        try:
            # Import ASHLAR modules
            from ashlar import reg
            import numpy as np
            import skimage.io
            
            self.root.after(0, self.log_message, "✓ ASHLAR modules imported successfully")
            
            # Create a simple metadata class for our files
            class SimpleMetadata:
                def __init__(self, file_list, width, height, overlap, pixel_size, layout, direction):
                    self.file_list = file_list
                    self.width = width
                    self.height = height
                    self.overlap = overlap
                    self._pixel_size = pixel_size
                    self.layout = layout
                    self.direction = direction
                    
                    # Read first image to get size and dtype
                    first_img = skimage.io.imread(file_list[0])
                    if first_img.ndim == 3:
                        first_img = first_img[:,:,0]  # Take first channel if RGB
                    self._tile_size = np.array(first_img.shape)
                    self._dtype = first_img.dtype
                    self._num_images = len(file_list)
                    self._num_channels = 1
                    
                    # Calculate positions
                    self._calculate_positions()
                
                def _calculate_positions(self):
                    positions = []
                    for i in range(self._num_images):
                        if self.direction == "horizontal":
                            row = i // self.width
                            col = i % self.width
                            if self.layout == "snake" and row % 2 == 1:
                                col = self.width - 1 - col
                        else:
                            col = i // self.height
                            row = i % self.height
                            if self.layout == "snake" and col % 2 == 1:
                                row = self.height - 1 - row
                        
                        pos_y = row * self._tile_size[0] * (1 - self.overlap)
                        pos_x = col * self._tile_size[1] * (1 - self.overlap)
                        positions.append([pos_y, pos_x])
                    
                    self._positions = np.array(positions)
                
                @property
                def num_images(self):
                    return self._num_images
                
                @property
                def num_channels(self):
                    return self._num_channels
                
                @property
                def pixel_size(self):
                    return self._pixel_size
                
                @property
                def pixel_dtype(self):
                    return self._dtype
                
                @property
                def positions(self):
                    return self._positions
                
                @property
                def size(self):
                    return self._tile_size
                
                def tile_position(self, i):
                    return self._positions[i]
                
                def tile_size(self, i):
                    return self._tile_size
            
            # Create a simple reader class
            class SimpleReader:
                def __init__(self, file_list, metadata):
                    self.file_list = file_list
                    self.metadata = metadata
                
                def read(self, series, c):
                    img = skimage.io.imread(self.file_list[series])
                    if img.ndim == 3:
                        img = img[:,:,0]  # Take first channel if RGB
                    return img
            
            # Create metadata and reader
            metadata = SimpleMetadata(
                self.file_list,
                self.width.get(),
                self.height.get(),
                self.overlap.get(),
                self.pixel_size.get(),
                self.layout.get(),
                self.direction.get()
            )
            
            reader = SimpleReader(self.file_list, metadata)
            
            self.root.after(0, self.log_message, f"✓ Created reader for {len(self.file_list)} files")
            self.root.after(0, self.log_message, f"  - Image size: {metadata.size}")
            self.root.after(0, self.log_message, f"  - Grid: {self.width.get()}x{self.height.get()}")
            
            # Apply axis flips if needed
            if self.flip_x.get() or self.flip_y.get():
                sx = -1 if self.flip_x.get() else 1
                sy = -1 if self.flip_y.get() else 1
                metadata._positions = metadata._positions * [sy, sx]
                self.root.after(0, self.log_message, f"✓ Applied axis flips: X={self.flip_x.get()}, Y={self.flip_y.get()}")
            
            # Create EdgeAligner
            self.root.after(0, self.log_message, "Starting edge alignment...")
            edge_aligner = reg.EdgeAligner(reader, verbose=True)
            edge_aligner.run()
            
            self.root.after(0, self.log_message, "✓ Edge alignment completed")
            self.root.after(0, self.log_message, f"  - Mosaic shape: {edge_aligner.mosaic_shape}")
            
            # Create Mosaic
            self.root.after(0, self.log_message, "Creating mosaic...")
            mosaic = reg.Mosaic(edge_aligner, edge_aligner.mosaic_shape, verbose=True)
            
            # Write output
            output_path = self.output_file.get()
            self.root.after(0, self.log_message, f"Writing output to {output_path}...")
            
            if output_path.endswith('.ome.tif'):
                writer = reg.PyramidWriter([mosaic], output_path, verbose=True)
            else:
                writer = reg.TiffListWriter([mosaic], output_path, verbose=True)
            
            writer.run()
            
            self.root.after(0, self.progress.stop)
            self.root.after(0, self.log_message, "=" * 50)
            self.root.after(0, self.log_message, "✓ Stitching completed successfully!")
            self.root.after(0, self.log_message, f"✓ Output saved to: {output_path}")
            self.root.after(0, messagebox.showinfo, "Success", "Stitching completed successfully!")
            
        except Exception as e:
            self.root.after(0, self.progress.stop)
            self.root.after(0, self.log_message, f"✗ Error: {str(e)}")
            self.root.after(0, messagebox.showerror, "Error", f"Error during stitching: {str(e)}")

def main():
    try:
        import ashlar
        print(f"ASHLAR version: {ashlar.__version__}")
    except ImportError:
        messagebox.showerror(
            "ASHLAR Not Found", 
            "ASHLAR not found. Please install:\npip install ashlar"
        )
        return
    
    root = tk.Tk()
    app = FlexibleAshlarGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
