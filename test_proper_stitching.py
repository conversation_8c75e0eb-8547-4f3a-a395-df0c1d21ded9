#!/usr/bin/env python3
"""
Test script for proper image stitching - simplified version
"""

import os
import sys

def test_imports():
    """Test if all required modules are available"""
    print("🔍 Testing imports...")
    
    try:
        import numpy as np
        print("✅ numpy imported successfully")
    except ImportError as e:
        print(f"❌ numpy import failed: {e}")
        return False
    
    try:
        import cv2
        print(f"✅ opencv imported successfully (version: {cv2.__version__})")
    except ImportError as e:
        print(f"❌ opencv import failed: {e}")
        return False
    
    try:
        import skimage
        print(f"✅ scikit-image imported successfully (version: {skimage.__version__})")
    except ImportError as e:
        print(f"❌ scikit-image import failed: {e}")
        return False
    
    return True

def test_sift():
    """Test SIFT detector"""
    print("\n🔍 Testing SIFT detector...")
    
    try:
        import cv2
        detector = cv2.SIFT_create(nfeatures=100)
        print("✅ SIFT detector created successfully")
        return True
    except Exception as e:
        print(f"❌ SIFT detector failed: {e}")
        
        # Try ORB as alternative
        try:
            detector = cv2.ORB_create(nfeatures=100)
            print("✅ ORB detector created as alternative")
            return True
        except Exception as e2:
            print(f"❌ ORB detector also failed: {e2}")
            return False

def test_folder():
    """Test if image folder exists"""
    folder_path = "D:/Stitch/bilinear_stitching_20250915_152529"
    
    if len(sys.argv) > 1:
        folder_path = sys.argv[1]
    
    print(f"\n📁 Testing folder: {folder_path}")
    
    if not os.path.exists(folder_path):
        print(f"❌ Folder not found: {folder_path}")
        return False, folder_path
    
    # Count images
    import glob
    png_files = glob.glob(os.path.join(folder_path, "*.png"))
    jpg_files = glob.glob(os.path.join(folder_path, "*.jpg"))
    tif_files = glob.glob(os.path.join(folder_path, "*.tif"))
    
    total_images = len(png_files) + len(jpg_files) + len(tif_files)
    
    print(f"✅ Folder found with {total_images} images")
    print(f"   - PNG files: {len(png_files)}")
    print(f"   - JPG files: {len(jpg_files)}")
    print(f"   - TIF files: {len(tif_files)}")
    
    return True, folder_path

def main():
    print("🧪 PROPER IMAGE STITCHING - COMPATIBILITY TEST")
    print("=" * 50)
    
    # Test 1: Imports
    if not test_imports():
        print("\n❌ FAILED: Missing required modules")
        print("Please install: pip install opencv-python scikit-image numpy")
        return
    
    # Test 2: SIFT detector
    if not test_sift():
        print("\n❌ FAILED: Feature detector not working")
        return
    
    # Test 3: Folder
    folder_ok, folder_path = test_folder()
    if not folder_ok:
        print(f"\n❌ FAILED: Image folder not accessible")
        return
    
    print("\n" + "=" * 50)
    print("✅ ALL TESTS PASSED!")
    print("=" * 50)
    print("🎯 Ready to run proper image stitching")
    print(f"📁 Image folder: {folder_path}")
    print("\n🚀 To run the full stitching pipeline:")
    print("   python proper_image_stitching.py")
    print("\n💡 Make sure you're in the correct environment:")
    print("   (env-ashlar) should be visible in your prompt")

if __name__ == "__main__":
    main()
