#!/usr/bin/env python3
"""
Create debug mosaic with grid overlay for analysis
"""

import os
import glob
import numpy as np
import skimage.io
import matplotlib.pyplot as plt
import matplotlib.patches as patches

def create_debug_mosaic_with_grid(folder_path):
    print("Creating debug mosaic with grid overlay...")
    
    # Get PNG files
    png_files = glob.glob(os.path.join(folder_path, "*.png"))
    png_files.sort()
    
    if not png_files:
        print("No PNG files found!")
        return
    
    # Read first image to get dimensions
    first_img = skimage.io.imread(png_files[0])
    is_color = first_img.ndim == 3
    
    if is_color:
        tile_height, tile_width, channels = first_img.shape
        print(f"✅ COLOR images detected: {channels} channels")
    else:
        tile_height, tile_width = first_img.shape
        print("⚠️ Grayscale images detected")
    
    num_images = len(png_files)
    print(f"Tile dimensions: {tile_width} x {tile_height}")
    print(f"Number of tiles: {num_images}")
    
    # Parameters
    grid_width = 9
    grid_height = 4
    overlap = 0.2
    
    # Calculate positions
    positions = []
    for i in range(num_images):
        row = i // grid_width
        col = i % grid_width
        
        # Snake pattern
        if row % 2 == 1:
            col = grid_width - 1 - col
        
        pos_y = row * tile_height * (1 - overlap)
        pos_x = col * tile_width * (1 - overlap)
        positions.append([pos_y, pos_x])
    
    positions = np.array(positions)
    
    # Apply flip Y and make positive
    positions = positions * [-1, 1]
    min_y = positions[:,0].min()
    min_x = positions[:,1].min()
    
    if min_y < 0:
        positions[:,0] -= min_y
    if min_x < 0:
        positions[:,1] -= min_x
    
    # Calculate mosaic size
    max_y = int(positions[:,0].max() + tile_height)
    max_x = int(positions[:,1].max() + tile_width)
    
    print(f"Mosaic size: {max_x} x {max_y}")
    
    # Create mosaic
    if is_color:
        mosaic = np.zeros((max_y, max_x, channels), dtype=np.uint8)
    else:
        mosaic = np.zeros((max_y, max_x), dtype=np.uint8)
    
    print("Placing tiles...")
    
    # Place tiles (simple placement without blending for debug)
    for i, (y, x) in enumerate(positions):
        if i >= len(png_files):
            break
        
        # Load image
        img = skimage.io.imread(png_files[i])
        
        # Calculate placement region
        y_start = int(y)
        y_end = y_start + tile_height
        x_start = int(x)
        x_end = x_start + tile_width
        
        # Ensure we don't go out of bounds
        y_end = min(y_end, max_y)
        x_end = min(x_end, max_x)
        
        # Get actual tile size
        actual_h = y_end - y_start
        actual_w = x_end - x_start
        
        if actual_h <= 0 or actual_w <= 0:
            continue
        
        # Place tile
        if is_color:
            mosaic[y_start:y_end, x_start:x_end, :] = img[:actual_h, :actual_w, :]
        else:
            mosaic[y_start:y_end, x_start:x_end] = img[:actual_h, :actual_w]
        
        if i % 10 == 0:
            print(f"Placed tile {i}/{num_images}")
    
    # Create visualization with grid
    print("Creating grid visualization...")
    
    # Create figure
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10))
    
    # Plot 1: Mosaic with grid overlay
    ax1.imshow(mosaic)
    ax1.set_title("Mosaic with Grid Overlay", fontsize=16)
    
    # Add grid lines and tile numbers
    for i, (y, x) in enumerate(positions):
        if i >= len(png_files):
            break
        
        # Draw rectangle
        rect = patches.Rectangle((x, y), tile_width, tile_height, 
                               linewidth=2, edgecolor='red', facecolor='none', alpha=0.7)
        ax1.add_patch(rect)
        
        # Add tile number
        ax1.text(x + tile_width/2, y + tile_height/2, str(i), 
                ha='center', va='center', fontsize=12, color='yellow', 
                bbox=dict(boxstyle="round,pad=0.3", facecolor='black', alpha=0.7))
    
    ax1.set_xlabel("X (pixels)")
    ax1.set_ylabel("Y (pixels)")
    
    # Plot 2: Position diagram
    ax2.scatter(positions[:,1], positions[:,0], c='red', s=100, alpha=0.7)
    for i, (y, x) in enumerate(positions):
        if i >= len(png_files):
            break
        ax2.text(x, y, str(i), ha='center', va='center', fontsize=10, color='white',
                bbox=dict(boxstyle="round,pad=0.2", facecolor='blue', alpha=0.8))
    
    ax2.set_title("Tile Positions in Mosaic", fontsize=16)
    ax2.set_xlabel("X (pixels)")
    ax2.set_ylabel("Y (pixels)")
    ax2.grid(True, alpha=0.3)
    ax2.invert_yaxis()  # Match image coordinates
    
    plt.tight_layout()
    
    # Save visualization
    debug_output = os.path.join(folder_path, "debug_mosaic_with_grid.png")
    plt.savefig(debug_output, dpi=150, bbox_inches='tight')
    print(f"Debug visualization saved: {debug_output}")
    
    # Save mosaic
    mosaic_output = os.path.join(folder_path, "debug_mosaic_color.png")
    skimage.io.imsave(mosaic_output, mosaic)
    print(f"Debug mosaic saved: {mosaic_output}")
    
    plt.show()
    
    # Print analysis
    print("\n" + "=" * 60)
    print("📊 MOSAIC ANALYSIS")
    print("=" * 60)
    print(f"Total tiles: {num_images}")
    print(f"Grid layout: {grid_width} x {grid_height}")
    print(f"Tile size: {tile_width} x {tile_height}")
    print(f"Overlap: {overlap*100:.1f}%")
    print(f"Final mosaic size: {max_x} x {max_y}")
    
    if is_color:
        print(f"Color channels: {channels}")
        print(f"Color mosaic: ✅ PRESERVED")
    else:
        print("Grayscale mosaic")
    
    print("\n🔍 TILE POSITIONS:")
    for i in range(min(10, num_images)):  # Show first 10 tiles
        y, x = positions[i]
        print(f"Tile {i:2d}: ({x:4.0f}, {y:4.0f})")
    
    if num_images > 10:
        print(f"... and {num_images-10} more tiles")
    
    return mosaic

def main():
    folder_path = "D:/Stitch/bilinear_stitching_20250915_152529"
    
    if len(os.sys.argv) > 1:
        folder_path = os.sys.argv[1]
    
    if not os.path.exists(folder_path):
        print(f"Folder not found: {folder_path}")
        return
    
    mosaic = create_debug_mosaic_with_grid(folder_path)
    
    print("\n" + "=" * 50)
    print("✅ DEBUG MOSAIC WITH GRID CREATED!")
    print("=" * 50)
    print("Files created:")
    print("- debug_mosaic_with_grid.png (grid visualization)")
    print("- debug_mosaic_color.png (color mosaic)")
    print("\nNow you can see the grid layout clearly!")

if __name__ == "__main__":
    main()
