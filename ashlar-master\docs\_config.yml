# Welcome to <PERSON><PERSON><PERSON>!
#
# This config file is meant for settings that affect your whole blog, values
# which you are expected to set up once and rarely edit after that. If you find
# yourself editing this file very often, consider using <PERSON><PERSON><PERSON>'s data files
# feature for the data you need to update frequently.
#
# For technical reasons, this file is *NOT* reloaded automatically when you use
# 'bundle exec jekyll serve'. If you change this file, please restart the server process.
#
# If you need help with YAML syntax, here are some quick references for you: 
# https://learn-the-web.algonquindesign.ca/topics/markdown-yaml-cheat-sheet/#yaml
# https://learnxinyminutes.com/docs/yaml/
#
# Site settings
# These are used to personalize your new site. If you look in the HTML files,
# you will see them accessed via {{ site.title }}, {{ site.email }}, and so on.
# You can create any custom variable you would like, and they will be accessible
# in the templates via {{ site.myvariable }}.

title: ASHLAR
description: >- # this means to ignore newlines until "logo:"
  ASHLAR website
logo: "/assets/images/ashlar_logo_process.png"
hero_background: "/assets/images/ashlar1.PNG"

# Build settings
remote_theme: labsyspharm/just-the-docs-lsp
color_scheme: blue
# color scheme supports "light" (default), "blue", "bold", and "dark"
heading_anchors: true

# Banner links to include
banner_links:
  lsp: true
  hits: true

# Back to top link
back_to_top: true
back_to_top_text: "Back to Top"

# License Information
license:
  - description: "Contents of this website is licensed under the "
    name: "CC0 1.0 License"
    url: "https://github.com/labsyspharm/ashlar/blob/master/docs/LICENSE"
  - description: "ASHLAR the software is licenced under the "
    name: "MIT License"
    url: "https://github.com/labsyspharm/ashlar/blob/master/LICENSE"

# Linked logos
footer_logos:
  - name: "Laboratory of Systems Pharmacology"
    image: "assets/images/logo_lsp_white.svg" # this is stored in the remote theme's repo
    url: "https://hits.harvard.edu/the-program/laboratory-of-systems-pharmacology/about/"
  - name: "Harvard Medical School"
    image: "/assets/images/logo_hms.svg" # this is stored in the remote theme's repo
    url: "https://hms.harvard.edu/"

# Footer last edited timestamp
last_edit_timestamp: false # show or hide edit time - page must have `last_modified_date` defined in the frontmatter
last_edit_time_format: "%b %e %Y" # format: https://ruby-doc.org/stdlib-2.7.0/libdoc/time/rdoc/Time.html

# Footer "Edit this page on GitHub" link text
gh_edit_link: true # show or hide edit this page link
gh_edit_link_text: "Edit this page on GitHub."
gh_edit_repository: "https://github.com/labsyspharm/ashlar" # the github URL for your repo
gh_edit_branch: "master" # the branch that your docs is served from
gh_edit_source: docs # the source that your files originate from
gh_edit_view_mode: "tree" # "tree" or "edit" if you want the user to jump into the editor immediately


plugins:
  - jekyll-remote-theme
  - jekyll-seo-tag
  - jekyll-include-cache
  - jekyll-feed

repository: labsyspharm/ashlar

exclude: ["node_modules/", "*.gemspec", "*.gem", "Gemfile", "Gemfile.lock", "package.json", "package-lock.json",  "script/", "LICENSE.txt", "lib/", "bin/", "README.md", "Rakefile", "vendor/"]
compress_html:
  clippings: all
  comments: all
  endings: all
  startings: []
  blanklines: false
  profile: false

# Exclude from processing.
# The following items will not be processed, by default.
# Any item listed under the `exclude:` key here will be automatically added to
# the internal "default list".
#
# Excluded items can be processed by explicitly listing the directories or
# their entries' file path in the `include:` list.
#
# exclude:
#   - .sass-cache/
#   - .jekyll-cache/
#   - gemfiles/
#   - Gemfile
#   - Gemfile.lock
#   - node_modules/
#   - vendor/bundle/
#   - vendor/cache/
#   - vendor/gems/
#   - vendor/ruby/
