#!/usr/bin/env python3
"""
Debug version - Simple hybrid without complex blending
Focus on getting the basic positioning right first
"""

import os
import glob
import numpy as np
import skimage.io
import matplotlib.pyplot as plt

def debug_hybrid_simple(folder_path):
    print("🔍 DEBUG HYBRID - Simple Version")
    print("=" * 50)
    
    # Get PNG files
    png_files = glob.glob(os.path.join(folder_path, "*.png"))
    png_files.sort()
    
    if not png_files:
        print("No PNG files found!")
        return
    
    print(f"Found {len(png_files)} PNG files")
    
    # Load first few images for testing
    test_images = []
    for i in range(min(5, len(png_files))):
        img = skimage.io.imread(png_files[i])
        test_images.append(img)
        print(f"Image {i}: shape={img.shape}, dtype={img.dtype}, min={img.min()}, max={img.max()}")
    
    first_img = test_images[0]
    is_color = first_img.ndim == 3
    
    if is_color:
        tile_height, tile_width, channels = first_img.shape
        print(f"✅ COLOR images: {tile_width}x{tile_height}, {channels} channels")
    else:
        tile_height, tile_width = first_img.shape
        channels = 1
        print(f"✅ GRAYSCALE images: {tile_width}x{tile_height}")
    
    # Grid parameters
    grid_width = 9
    grid_height = 4
    overlap = 0.2
    num_images = len(png_files)
    
    print(f"Grid: {grid_width}x{grid_height}, Overlap: {overlap*100:.1f}%")
    
    # Calculate positions
    print("\n📐 Calculating positions...")
    positions = []
    for i in range(num_images):
        row = i // grid_width
        col = i % grid_width
        
        # Snake pattern
        if row % 2 == 1:
            col = grid_width - 1 - col
        
        pos_y = row * tile_height * (1 - overlap)
        pos_x = col * tile_width * (1 - overlap)
        positions.append([pos_y, pos_x])
    
    positions = np.array(positions)
    
    # Apply flip Y and make positive
    positions = positions * [-1, 1]
    min_y = positions[:,0].min()
    min_x = positions[:,1].min()
    
    if min_y < 0:
        positions[:,0] -= min_y
    if min_x < 0:
        positions[:,1] -= min_x
    
    print(f"Position range: X=[{positions[:,1].min():.1f}, {positions[:,1].max():.1f}], Y=[{positions[:,0].min():.1f}, {positions[:,0].max():.1f}]")
    
    # Calculate mosaic size
    max_y = int(positions[:,0].max() + tile_height)
    max_x = int(positions[:,1].max() + tile_width)
    
    print(f"Mosaic size: {max_x} x {max_y}")
    
    # Create simple mosaic (no blending, just placement)
    print("\n🎨 Creating simple mosaic...")
    
    if is_color:
        mosaic = np.zeros((max_y, max_x, channels), dtype=np.uint8)
    else:
        mosaic = np.zeros((max_y, max_x), dtype=np.uint8)
    
    # Place first 10 tiles for debugging
    for i in range(min(10, len(png_files))):
        print(f"\nProcessing tile {i}:")
        
        # Load image
        img = skimage.io.imread(png_files[i])
        print(f"  Loaded: shape={img.shape}, min={img.min()}, max={img.max()}")
        
        y, x = positions[i]
        print(f"  Position: ({x:.1f}, {y:.1f})")
        
        # Calculate placement region
        y_start = int(y)
        y_end = y_start + tile_height
        x_start = int(x)
        x_end = x_start + tile_width
        
        print(f"  Placement: [{x_start}:{x_end}, {y_start}:{y_end}]")
        
        # Ensure bounds
        y_end = min(y_end, max_y)
        x_end = min(x_end, max_x)
        
        actual_h = y_end - y_start
        actual_w = x_end - x_start
        
        print(f"  Actual size: {actual_w} x {actual_h}")
        
        if actual_h <= 0 or actual_w <= 0:
            print(f"  ❌ Invalid size, skipping")
            continue
        
        # Simple placement (last tile wins)
        if is_color:
            mosaic[y_start:y_end, x_start:x_end, :] = img[:actual_h, :actual_w, :]
        else:
            mosaic[y_start:y_end, x_start:x_end] = img[:actual_h, :actual_w]
        
        print(f"  ✅ Placed successfully")
    
    # Check mosaic statistics
    print(f"\n📊 Mosaic statistics:")
    print(f"Shape: {mosaic.shape}")
    print(f"Dtype: {mosaic.dtype}")
    print(f"Min: {mosaic.min()}")
    print(f"Max: {mosaic.max()}")
    print(f"Mean: {mosaic.mean():.2f}")
    print(f"Non-zero pixels: {np.count_nonzero(mosaic)}")
    print(f"Total pixels: {mosaic.size}")
    print(f"Coverage: {np.count_nonzero(mosaic) / mosaic.size * 100:.2f}%")
    
    # Save debug mosaic
    output_path = os.path.join(folder_path, "DEBUG_hybrid_simple.png")
    skimage.io.imsave(output_path, mosaic)
    print(f"\n💾 Debug mosaic saved: {output_path}")
    
    # Create visualization
    plt.figure(figsize=(15, 8))
    
    # Plot 1: Mosaic
    plt.subplot(1, 2, 1)
    if is_color:
        plt.imshow(mosaic)
    else:
        plt.imshow(mosaic, cmap='gray')
    plt.title("Debug Mosaic (First 10 tiles)")
    plt.axis('off')
    
    # Plot 2: Positions
    plt.subplot(1, 2, 2)
    plt.scatter(positions[:10, 1], positions[:10, 0], c='red', s=100, alpha=0.7)
    for i in range(min(10, len(positions))):
        plt.text(positions[i, 1], positions[i, 0], str(i), 
                ha='center', va='center', fontsize=10, color='white',
                bbox=dict(boxstyle="round,pad=0.2", facecolor='blue', alpha=0.8))
    
    plt.title("Tile Positions (First 10)")
    plt.xlabel("X (pixels)")
    plt.ylabel("Y (pixels)")
    plt.grid(True, alpha=0.3)
    plt.gca().invert_yaxis()
    
    plt.tight_layout()
    
    # Save visualization
    viz_path = os.path.join(folder_path, "DEBUG_hybrid_visualization.png")
    plt.savefig(viz_path, dpi=150, bbox_inches='tight')
    print(f"📊 Visualization saved: {viz_path}")
    
    plt.show()
    
    return mosaic

def main():
    folder_path = "D:/Stitch/bilinear_stitching_20250915_152529"
    
    if len(os.sys.argv) > 1:
        folder_path = os.sys.argv[1]
    
    if not os.path.exists(folder_path):
        print(f"Folder not found: {folder_path}")
        return
    
    try:
        result = debug_hybrid_simple(folder_path)
        
        print("\n" + "=" * 50)
        print("🔍 DEBUG COMPLETED!")
        print("=" * 50)
        print("Check the debug files:")
        print("- DEBUG_hybrid_simple.png")
        print("- DEBUG_hybrid_visualization.png")
        print("\nIf this works, we can add blending back")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
