#!/usr/bin/env python3
"""
ASHLAR Simple Working GUI - No pattern parsing, just file order
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import threading
import glob

class SimpleWorkingGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("ASHLAR Simple Working GUI")
        self.root.geometry("700x600")
        
        # Variables
        self.input_folder = tk.StringVar()
        self.output_file = tk.StringVar()
        self.overlap = tk.DoubleVar(value=0.2)
        self.width = tk.IntVar(value=6)
        self.height = tk.IntVar(value=6)
        self.flip_y = tk.BooleanVar(value=True)
        
        # File list
        self.file_list = []
        
        self.create_widgets()
        
    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = ttk.Label(main_frame, text="ASHLAR Simple Working GUI", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Input/Output Section
        io_frame = ttk.LabelFrame(main_frame, text="Input/Output", padding=10)
        io_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Input folder
        ttk.Label(io_frame, text="Input Folder:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(io_frame, textvariable=self.input_folder, width=50).grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(io_frame, text="Browse", command=self.browse_input_folder).grid(row=0, column=2, pady=5)
        
        # Output file
        ttk.Label(io_frame, text="Output File:").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(io_frame, textvariable=self.output_file, width=50).grid(row=1, column=1, padx=5, pady=5)
        ttk.Button(io_frame, text="Browse", command=self.browse_output_file).grid(row=1, column=2, pady=5)
        
        # Settings Section
        settings_frame = ttk.LabelFrame(main_frame, text="Settings", padding=10)
        settings_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Grid settings
        ttk.Label(settings_frame, text="Grid Width:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(settings_frame, textvariable=self.width, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(settings_frame, text="Grid Height:").grid(row=0, column=2, sticky=tk.W, padx=(20,0), pady=5)
        ttk.Entry(settings_frame, textvariable=self.height, width=10).grid(row=0, column=3, sticky=tk.W, padx=5, pady=5)
        
        # Overlap
        ttk.Label(settings_frame, text="Overlap (0.0-1.0):").grid(row=1, column=0, sticky=tk.W, pady=5)
        ttk.Entry(settings_frame, textvariable=self.overlap, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Flip Y
        ttk.Checkbutton(settings_frame, text="Flip Y (start from bottom)", variable=self.flip_y).grid(row=1, column=2, columnspan=2, sticky=tk.W, padx=20, pady=5)
        
        # Control Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(button_frame, text="Load Files", command=self.load_files).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Start Stitching", command=self.start_stitching).pack(side=tk.RIGHT, padx=5)
        
        # Progress and Log Section
        log_frame = ttk.LabelFrame(main_frame, text="Progress & Log", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Progress bar
        self.progress = ttk.Progressbar(log_frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=(0, 10))
        
        # Log text area
        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, width=80)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
    def browse_input_folder(self):
        folder = filedialog.askdirectory(title="Select Input Folder")
        if folder:
            self.input_folder.set(folder)
            if not self.output_file.get():
                output_path = os.path.join(folder, "stitched_output.ome.tif")
                self.output_file.set(output_path)
            self.load_files()
    
    def browse_output_file(self):
        file = filedialog.asksaveasfilename(
            title="Save Stitched Image As",
            defaultextension=".ome.tif",
            filetypes=[("OME-TIFF files", "*.ome.tif"), ("TIFF files", "*.tif")]
        )
        if file:
            self.output_file.set(file)
    
    def load_files(self):
        if not self.input_folder.get():
            return
            
        try:
            folder = self.input_folder.get()
            png_files = glob.glob(os.path.join(folder, "*.png"))
            
            if not png_files:
                self.log_message("No PNG files found")
                return
            
            # Sort files alphabetically
            png_files.sort()
            self.file_list = png_files
            
            total_tiles = len(png_files)
            
            # Auto-suggest grid for 36 tiles
            if total_tiles == 36:
                self.width.set(6)
                self.height.set(6)
            
            self.log_message(f"✓ Loaded {total_tiles} PNG files")
            self.log_message(f"✓ First file: {os.path.basename(png_files[0])}")
            self.log_message(f"✓ Last file: {os.path.basename(png_files[-1])}")
            self.log_message(f"✓ Suggested grid: {self.width.get()}x{self.height.get()}")
            
        except Exception as e:
            self.log_message(f"Error loading files: {str(e)}")
    
    def log_message(self, message):
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update()
    
    def start_stitching(self):
        try:
            if not self.file_list:
                raise ValueError("No files loaded. Please select input folder first.")
            if not self.output_file.get():
                raise ValueError("Please specify output file")
            
            self.log_message("=" * 50)
            self.log_message("Starting ASHLAR stitching...")
            self.log_message("=" * 50)
            
            self.progress.start()
            
            thread = threading.Thread(target=self.run_stitching)
            thread.daemon = True
            thread.start()
            
        except Exception as e:
            messagebox.showerror("Error", f"Error: {str(e)}")
    
    def run_stitching(self):
        try:
            # Import required modules
            from ashlar import reg
            import numpy as np
            import skimage.io
            
            self.root.after(0, self.log_message, "✓ Imported ASHLAR modules")
            
            # Read first image to get dimensions
            first_img = skimage.io.imread(self.file_list[0])
            if first_img.ndim == 3:
                first_img = first_img[:,:,0]  # Take first channel if RGB
            
            tile_height, tile_width = first_img.shape
            num_images = len(self.file_list)
            
            self.root.after(0, self.log_message, f"✓ Image dimensions: {tile_width}x{tile_height}")
            self.root.after(0, self.log_message, f"✓ Number of tiles: {num_images}")
            
            # Calculate tile positions
            positions = []
            width = self.width.get()
            height = self.height.get()
            overlap = self.overlap.get()
            
            for i in range(num_images):
                row = i // width
                col = i % width
                
                # Snake pattern: reverse odd rows
                if row % 2 == 1:
                    col = width - 1 - col
                
                pos_y = row * tile_height * (1 - overlap)
                pos_x = col * tile_width * (1 - overlap)
                positions.append([pos_y, pos_x])
            
            positions = np.array(positions)
            
            # Apply flip Y if needed
            if self.flip_y.get():
                positions = positions * [-1, 1]
                self.root.after(0, self.log_message, "✓ Applied Y flip")
            
            self.root.after(0, self.log_message, "✓ Calculated tile positions")
            
            # Create simple metadata class
            class SimpleMetadata:
                def __init__(self, positions, tile_size, dtype, num_images):
                    self._positions = positions
                    self._tile_size = np.array(tile_size)
                    self._dtype = dtype
                    self._num_images = num_images
                    self._num_channels = 1
                    self._pixel_size = 1.0
                
                @property
                def num_images(self):
                    return self._num_images
                
                @property
                def num_channels(self):
                    return self._num_channels
                
                @property
                def pixel_size(self):
                    return self._pixel_size
                
                @property
                def pixel_dtype(self):
                    return self._dtype
                
                @property
                def positions(self):
                    return self._positions
                
                @property
                def size(self):
                    return self._tile_size
                
                def tile_position(self, i):
                    return self._positions[i]
                
                def tile_size(self, i):
                    return self._tile_size
            
            # Create simple reader class
            class SimpleReader:
                def __init__(self, file_list, metadata):
                    self.file_list = file_list
                    self.metadata = metadata
                
                def read(self, series, c):
                    img = skimage.io.imread(self.file_list[series])
                    if img.ndim == 3:
                        img = img[:,:,0]  # Take first channel if RGB
                    return img
            
            # Create metadata and reader
            metadata = SimpleMetadata(positions, (tile_height, tile_width), first_img.dtype, num_images)
            reader = SimpleReader(self.file_list, metadata)
            
            self.root.after(0, self.log_message, "✓ Created reader and metadata")
            
            # Run edge alignment
            self.root.after(0, self.log_message, "Starting edge alignment...")
            edge_aligner = reg.EdgeAligner(reader, verbose=True)
            edge_aligner.run()
            
            self.root.after(0, self.log_message, "✓ Edge alignment completed")
            
            # Create mosaic
            self.root.after(0, self.log_message, "Creating mosaic...")
            mosaic = reg.Mosaic(edge_aligner, edge_aligner.mosaic_shape, verbose=True)
            
            # Write output
            output_path = self.output_file.get()
            self.root.after(0, self.log_message, f"Writing output to {output_path}...")
            
            if output_path.endswith('.ome.tif'):
                writer = reg.PyramidWriter([mosaic], output_path, verbose=True)
            else:
                writer = reg.TiffListWriter([mosaic], output_path, verbose=True)
            
            writer.run()
            
            self.root.after(0, self.progress.stop)
            self.root.after(0, self.log_message, "=" * 50)
            self.root.after(0, self.log_message, "✓ Stitching completed successfully!")
            self.root.after(0, self.log_message, f"✓ Output saved to: {output_path}")
            self.root.after(0, messagebox.showinfo, "Success", "Stitching completed!")
            
        except Exception as e:
            self.root.after(0, self.progress.stop)
            self.root.after(0, self.log_message, f"✗ Error: {str(e)}")
            self.root.after(0, messagebox.showerror, "Error", f"Error: {str(e)}")

def main():
    try:
        import ashlar
        print(f"ASHLAR version: {ashlar.__version__}")
    except ImportError:
        messagebox.showerror(
            "ASHLAR Not Found", 
            "ASHLAR not found. Please install:\npip install ashlar"
        )
        return
    
    root = tk.Tk()
    app = SimpleWorkingGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
