#!/usr/bin/env python3
"""
Create final mosaic with MINIMAL blending to preserve image sharpness
Optimized for biological samples where detail preservation is critical
"""

import os
import glob
import numpy as np
import skimage.io
import tifffile

def create_sharp_weight(shape, overlap_pixels):
    """Create sharp weight for minimal blending - preserves detail"""
    h, w = shape
    y, x = np.ogrid[:h, :w]
    
    # Distance from edges
    dist_top = y
    dist_bottom = h - 1 - y
    dist_left = x
    dist_right = w - 1 - x
    
    # Minimum distance to any edge
    dist_to_edge = np.minimum(np.minimum(dist_top, dist_bottom), 
                             np.minimum(dist_left, dist_right))
    
    # SHARP blending: only blend in very narrow edge region
    # Most of the tile keeps full weight (1.0)
    blend_zone = max(1, overlap_pixels // 4)  # Much smaller blend zone
    weight = np.minimum(dist_to_edge / blend_zone, 1.0)
    
    # Apply power function to make transition even sharper
    weight = np.power(weight, 0.5)  # Square root for sharper transition
    
    return weight

def create_sharp_mosaic(folder_path):
    print("Creating SHARP mosaic with minimal blending...")
    print("🔬 Optimized for biological sample detail preservation")
    
    # Get PNG files
    png_files = glob.glob(os.path.join(folder_path, "*.png"))
    png_files.sort()
    
    if not png_files:
        print("No PNG files found!")
        return
    
    # Read first image to get dimensions and check if color
    first_img = skimage.io.imread(png_files[0])
    is_color = first_img.ndim == 3
    
    if is_color:
        tile_height, tile_width, channels = first_img.shape
        print(f"✅ COLOR images detected: {channels} channels")
    else:
        tile_height, tile_width = first_img.shape
        channels = 1
        print("⚠️ Grayscale images detected")
    
    num_images = len(png_files)
    print(f"Tile dimensions: {tile_width} x {tile_height}")
    print(f"Number of tiles: {num_images}")
    
    # Parameters - REDUCED overlap for sharper result
    grid_width = 9
    grid_height = 4
    overlap = 0.15  # Reduced from 0.2 to 0.15 for less blending
    
    # Calculate positions
    positions = []
    for i in range(num_images):
        row = i // grid_width
        col = i % grid_width
        
        # Snake pattern
        if row % 2 == 1:
            col = grid_width - 1 - col
        
        pos_y = row * tile_height * (1 - overlap)
        pos_x = col * tile_width * (1 - overlap)
        positions.append([pos_y, pos_x])
    
    positions = np.array(positions)
    
    # Apply flip Y and make positive
    positions = positions * [-1, 1]
    min_y = positions[:,0].min()
    min_x = positions[:,1].min()
    
    if min_y < 0:
        positions[:,0] -= min_y
    if min_x < 0:
        positions[:,1] -= min_x
    
    # Calculate mosaic size
    max_y = int(positions[:,0].max() + tile_height)
    max_x = int(positions[:,1].max() + tile_width)
    
    print(f"Mosaic size: {max_x} x {max_y}")
    print(f"Overlap reduced to: {overlap*100:.1f}% for sharper result")
    
    # Create mosaic arrays for color
    if is_color:
        mosaic = np.zeros((max_y, max_x, channels), dtype=np.float64)
        weight_sum = np.zeros((max_y, max_x), dtype=np.float64)
    else:
        mosaic = np.zeros((max_y, max_x), dtype=np.float64)
        weight_sum = np.zeros((max_y, max_x), dtype=np.float64)
    
    # Calculate overlap in pixels - SMALLER blend zone
    overlap_pixels = int(min(tile_height, tile_width) * overlap / 3)  # Reduced blend zone
    print(f"Sharp blend zone: {overlap_pixels} pixels (minimal blending)")
    
    # Create SHARP weight template
    tile_weight = create_sharp_weight((tile_height, tile_width), overlap_pixels)
    
    print("Placing tiles with MINIMAL blending...")
    
    # Place tiles with minimal weighted blending
    for i, (y, x) in enumerate(positions):
        if i >= len(png_files):
            break
        
        # Load image
        img = skimage.io.imread(png_files[i])
        
        # Convert to float (keep all channels for color)
        img_float = img.astype(np.float64)
        
        # Calculate placement region
        y_start = int(y)
        y_end = y_start + tile_height
        x_start = int(x)
        x_end = x_start + tile_width
        
        # Ensure we don't go out of bounds
        y_end = min(y_end, max_y)
        x_end = min(x_end, max_x)
        
        # Get actual tile size (in case of boundary)
        actual_h = y_end - y_start
        actual_w = x_end - x_start
        
        if actual_h <= 0 or actual_w <= 0:
            continue
        
        # Get corresponding regions
        if is_color:
            img_region = img_float[:actual_h, :actual_w, :]
            weight_region = tile_weight[:actual_h, :actual_w]
            
            # Add to mosaic with weights (broadcast weight across channels)
            for c in range(channels):
                mosaic[y_start:y_end, x_start:x_end, c] += img_region[:,:,c] * weight_region
        else:
            img_region = img_float[:actual_h, :actual_w]
            weight_region = tile_weight[:actual_h, :actual_w]
            mosaic[y_start:y_end, x_start:x_end] += img_region * weight_region
        
        weight_sum[y_start:y_end, x_start:x_end] += weight_region
        
        if i % 10 == 0:
            print(f"Placed tile {i}/{num_images}")
    
    # Normalize by weights
    print("Normalizing sharp mosaic...")
    
    # Avoid division by zero
    weight_sum[weight_sum == 0] = 1
    
    if is_color:
        # Normalize each channel
        for c in range(channels):
            mosaic[:,:,c] = mosaic[:,:,c] / weight_sum
    else:
        mosaic = mosaic / weight_sum
    
    # Convert back to original dtype
    mosaic = mosaic.astype(first_img.dtype)
    
    # Save results
    print("Saving SHARP results...")
    
    # Save as PNG
    png_output = os.path.join(folder_path, "final_mosaic_SHARP.png")
    skimage.io.imsave(png_output, mosaic)
    print(f"PNG mosaic saved: {png_output}")
    
    # Save as TIFF
    tiff_output = os.path.join(folder_path, "final_mosaic_SHARP.tif")
    tifffile.imwrite(tiff_output, mosaic)
    print(f"TIFF mosaic saved: {tiff_output}")
    
    # Save as OME-TIFF (compatible with ImageJ/FIJI)
    ome_output = os.path.join(folder_path, "final_mosaic_SHARP.ome.tif")
    tifffile.imwrite(ome_output, mosaic, 
                    metadata={'axes': 'YX', 'PhysicalSizeX': 1.0, 'PhysicalSizeY': 1.0})
    print(f"OME-TIFF mosaic saved: {ome_output}")
    
    # Statistics
    print(f"\nSHARP mosaic statistics:")
    print(f"Size: {mosaic.shape[1]} x {mosaic.shape[0]} pixels")
    print(f"Mean intensity: {np.mean(mosaic):.1f}")
    print(f"Std intensity: {np.std(mosaic):.1f}")
    print(f"Min intensity: {np.min(mosaic)}")
    print(f"Max intensity: {np.max(mosaic)}")
    print(f"Non-zero pixels: {np.count_nonzero(mosaic)} / {mosaic.size} ({100*np.count_nonzero(mosaic)/mosaic.size:.1f}%)")
    
    return mosaic

def main():
    folder_path = "D:/Stitch/bilinear_stitching_20250915_152529"
    
    if len(os.sys.argv) > 1:
        folder_path = os.sys.argv[1]
    
    if not os.path.exists(folder_path):
        print(f"Folder not found: {folder_path}")
        return
    
    mosaic = create_sharp_mosaic(folder_path)
    
    print("\n" + "=" * 50)
    print("🔬 SHARP MOSAIC CREATED SUCCESSFULLY!")
    print("=" * 50)
    print("Files created:")
    print("- final_mosaic_SHARP.png (for viewing)")
    print("- final_mosaic_SHARP.tif (for analysis)")
    print("- final_mosaic_SHARP.ome.tif (for ImageJ/FIJI)")
    print("\n🎯 OPTIMIZED FOR:")
    print("✅ Maximum detail preservation")
    print("✅ Minimal blending artifacts")
    print("✅ Sharp biological structures")
    print("✅ Reduced overlap for clarity")

if __name__ == "__main__":
    main()
