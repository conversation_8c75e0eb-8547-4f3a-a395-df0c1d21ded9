#!/usr/bin/env python3
"""
Debug tile positions and create a simple mosaic manually
"""

import os
import glob
import numpy as np
import skimage.io
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle

def debug_positions(folder_path):
    print("Debugging tile positions...")
    
    # Get PNG files
    png_files = glob.glob(os.path.join(folder_path, "*.png"))
    png_files.sort()
    
    if not png_files:
        print("No PNG files found!")
        return
    
    # Read first image to get dimensions
    first_img = skimage.io.imread(png_files[0])
    if first_img.ndim == 3:
        first_img = first_img[:,:,0]
    
    tile_height, tile_width = first_img.shape
    num_images = len(png_files)
    
    print(f"Tile dimensions: {tile_width} x {tile_height}")
    print(f"Number of tiles: {num_images}")
    
    # Parameters
    grid_width = 9
    grid_height = 4
    overlap = 0.2
    
    print(f"Grid: {grid_width} x {grid_height}")
    print(f"Overlap: {overlap}")
    
    # Calculate positions
    positions = []
    for i in range(num_images):
        row = i // grid_width
        col = i % grid_width
        
        # Snake pattern
        if row % 2 == 1:
            col = grid_width - 1 - col
        
        pos_y = row * tile_height * (1 - overlap)
        pos_x = col * tile_width * (1 - overlap)
        positions.append([pos_y, pos_x])
    
    positions = np.array(positions)
    
    print(f"\nOriginal positions:")
    print(f"Y range: {positions[:,0].min():.0f} to {positions[:,0].max():.0f}")
    print(f"X range: {positions[:,1].min():.0f} to {positions[:,1].max():.0f}")
    
    # Apply flip Y
    positions_flipped = positions * [-1, 1]
    print(f"\nAfter Y flip:")
    print(f"Y range: {positions_flipped[:,0].min():.0f} to {positions_flipped[:,0].max():.0f}")
    print(f"X range: {positions_flipped[:,1].min():.0f} to {positions_flipped[:,1].max():.0f}")
    
    # Shift to make all positions positive
    min_y = positions_flipped[:,0].min()
    min_x = positions_flipped[:,1].min()
    
    if min_y < 0:
        positions_flipped[:,0] -= min_y
        print(f"Shifted Y by {-min_y:.0f} to make positive")
    
    if min_x < 0:
        positions_flipped[:,1] -= min_x
        print(f"Shifted X by {-min_x:.0f} to make positive")
    
    print(f"\nFinal positions:")
    print(f"Y range: {positions_flipped[:,0].min():.0f} to {positions_flipped[:,0].max():.0f}")
    print(f"X range: {positions_flipped[:,1].min():.0f} to {positions_flipped[:,1].max():.0f}")
    
    # Calculate mosaic size
    max_y = positions_flipped[:,0].max() + tile_height
    max_x = positions_flipped[:,1].max() + tile_width
    
    print(f"\nMosaic size: {max_x:.0f} x {max_y:.0f}")
    
    # Visualize positions
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Plot 1: Grid layout
    for i, (y, x) in enumerate(positions_flipped):
        rect = Rectangle((x, y), tile_width, tile_height, 
                        linewidth=1, edgecolor='blue', facecolor='lightblue', alpha=0.5)
        ax1.add_patch(rect)
        ax1.text(x + tile_width/2, y + tile_height/2, str(i), 
                ha='center', va='center', fontsize=8)
    
    ax1.set_xlim(0, max_x)
    ax1.set_ylim(0, max_y)
    ax1.set_aspect('equal')
    ax1.set_title('Tile Positions in Mosaic')
    ax1.set_xlabel('X (pixels)')
    ax1.set_ylabel('Y (pixels)')
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Sample images in grid
    sample_indices = [0, 1, 8, 9, 10, 17, 18, 19, 26, 27, 28, 35]  # Corner and edge tiles
    sample_indices = [i for i in sample_indices if i < num_images]
    
    for idx, i in enumerate(sample_indices[:12]):
        if idx >= 12:
            break
        
        img = skimage.io.imread(png_files[i])
        if img.ndim == 3:
            img = img[:,:,0]
        
        row_plot = idx // 4
        col_plot = idx % 4
        
        if row_plot < 3 and col_plot < 4:
            ax2_sub = plt.subplot(3, 4, idx + 1)
            ax2_sub.imshow(img, cmap='gray')
            ax2_sub.set_title(f'Tile {i}')
            ax2_sub.axis('off')
    
    plt.tight_layout()
    plt.savefig('debug_positions.png', dpi=150, bbox_inches='tight')
    print(f"\nDebug visualization saved as 'debug_positions.png'")
    plt.show()
    
    # Create simple mosaic manually
    print("\nCreating simple mosaic manually...")
    
    # Create empty mosaic
    mosaic = np.zeros((int(max_y), int(max_x)), dtype=first_img.dtype)
    
    # Place tiles
    for i, (y, x) in enumerate(positions_flipped):
        if i >= len(png_files):
            break
            
        img = skimage.io.imread(png_files[i])
        if img.ndim == 3:
            img = img[:,:,0]
        
        y_start = int(y)
        y_end = y_start + tile_height
        x_start = int(x)
        x_end = x_start + tile_width
        
        # Simple placement (no blending)
        mosaic[y_start:y_end, x_start:x_end] = img
        
        if i < 5:  # Log first few placements
            print(f"Placed tile {i} at ({x_start}, {y_start}) to ({x_end}, {y_end})")
    
    # Save simple mosaic
    output_path = os.path.join(folder_path, "simple_mosaic.png")
    skimage.io.imsave(output_path, mosaic)
    print(f"Simple mosaic saved as: {output_path}")
    
    # Show mosaic thumbnail
    plt.figure(figsize=(12, 8))
    plt.imshow(mosaic, cmap='gray')
    plt.title('Simple Manual Mosaic')
    plt.axis('off')
    plt.savefig('simple_mosaic_preview.png', dpi=150, bbox_inches='tight')
    print("Mosaic preview saved as 'simple_mosaic_preview.png'")
    plt.show()
    
    # Statistics
    print(f"\nMosaic statistics:")
    print(f"Mean intensity: {np.mean(mosaic):.1f}")
    print(f"Std intensity: {np.std(mosaic):.1f}")
    print(f"Min intensity: {np.min(mosaic)}")
    print(f"Max intensity: {np.max(mosaic)}")
    print(f"Non-zero pixels: {np.count_nonzero(mosaic)} / {mosaic.size} ({100*np.count_nonzero(mosaic)/mosaic.size:.1f}%)")

def main():
    folder_path = "D:/Stitch/bilinear_stitching_20250915_152529"
    
    if len(os.sys.argv) > 1:
        folder_path = os.sys.argv[1]
    
    if not os.path.exists(folder_path):
        print(f"Folder not found: {folder_path}")
        return
    
    debug_positions(folder_path)

if __name__ == "__main__":
    main()
