#!/usr/bin/env python3
"""
HYBRID STITCHING - Best of Both Worlds
Grid positioning (accurate) + Advanced blending (seamless)
"""

import os
import glob
import numpy as np
import cv2
import skimage.io
from scipy import ndimage
from skimage import filters, exposure
import tifffile

def create_seamless_weight(shape, blend_width=50):
    """Create seamless blending weight using distance transform"""
    h, w = shape
    
    # Create binary mask (1 inside, 0 at border)
    mask = np.ones((h, w), dtype=np.float64)
    mask[0, :] = 0  # top edge
    mask[-1, :] = 0  # bottom edge
    mask[:, 0] = 0  # left edge
    mask[:, -1] = 0  # right edge
    
    # Distance transform from edges
    distance = ndimage.distance_transform_edt(mask)
    
    # Create smooth weight: 0 at edges, 1 in center
    weight = np.minimum(distance / blend_width, 1.0)
    
    # Apply smooth transition function
    weight = 0.5 * (1 + np.tanh(4 * (weight - 0.5)))
    
    return weight

def apply_exposure_matching(images, reference_idx=0):
    """Match exposure across all images"""
    print("🔧 Applying exposure matching...")
    
    # Calculate reference histogram
    ref_img = images[reference_idx]
    if ref_img.ndim == 3:
        ref_gray = cv2.cvtColor(ref_img, cv2.COLOR_BGR2GRAY)
    else:
        ref_gray = ref_img
    
    ref_mean = np.mean(ref_gray[ref_gray > 0])  # Ignore black pixels
    
    matched_images = []
    
    for i, img in enumerate(images):
        if img.ndim == 3:
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        else:
            gray = img
        
        # Calculate current mean (ignore black pixels)
        current_mean = np.mean(gray[gray > 0])
        
        if current_mean > 0:
            # Calculate gain
            gain = ref_mean / current_mean
            
            # Apply gain with clipping
            corrected = img.astype(np.float64) * gain
            corrected = np.clip(corrected, 0, 255).astype(np.uint8)
            
            matched_images.append(corrected)
            
            if i % 10 == 0:
                print(f"Image {i}: gain = {gain:.3f}")
        else:
            matched_images.append(img)
    
    print("✅ Exposure matching completed")
    return matched_images

def create_hybrid_mosaic(folder_path):
    print("🎯 HYBRID STITCHING - Grid + Advanced Blending")
    print("=" * 60)
    
    # Get PNG files
    png_files = glob.glob(os.path.join(folder_path, "*.png"))
    png_files.sort()
    
    if not png_files:
        print("No PNG files found!")
        return
    
    # Load images
    print("📁 Loading images...")
    images = []
    for i, img_path in enumerate(png_files):
        img = skimage.io.imread(img_path)
        images.append(img)
        if i % 10 == 0:
            print(f"Loaded {i+1}/{len(png_files)} images")
    
    # Check image properties
    first_img = images[0]
    is_color = first_img.ndim == 3
    
    if is_color:
        tile_height, tile_width, channels = first_img.shape
        print(f"✅ COLOR images: {tile_width}x{tile_height}, {channels} channels")
    else:
        tile_height, tile_width = first_img.shape
        channels = 1
        print(f"✅ GRAYSCALE images: {tile_width}x{tile_height}")
    
    num_images = len(images)
    print(f"Total images: {num_images}")
    
    # Apply exposure matching
    images = apply_exposure_matching(images)
    
    # Grid parameters (from previous analysis)
    grid_width = 9
    grid_height = 4
    overlap = 0.2
    
    print(f"Grid layout: {grid_width} x {grid_height}")
    print(f"Overlap: {overlap*100:.1f}%")
    
    # Calculate positions using proven grid method
    print("📐 Calculating grid positions...")
    positions = []
    for i in range(num_images):
        row = i // grid_width
        col = i % grid_width
        
        # Snake pattern
        if row % 2 == 1:
            col = grid_width - 1 - col
        
        pos_y = row * tile_height * (1 - overlap)
        pos_x = col * tile_width * (1 - overlap)
        positions.append([pos_y, pos_x])
    
    positions = np.array(positions)
    
    # Apply flip Y and make positive
    positions = positions * [-1, 1]
    min_y = positions[:,0].min()
    min_x = positions[:,1].min()
    
    if min_y < 0:
        positions[:,0] -= min_y
    if min_x < 0:
        positions[:,1] -= min_x
    
    # Calculate mosaic size
    max_y = int(positions[:,0].max() + tile_height)
    max_x = int(positions[:,1].max() + tile_width)
    
    print(f"Mosaic size: {max_x} x {max_y}")
    
    # Create advanced blending weight
    blend_width = int(min(tile_height, tile_width) * overlap / 2)
    tile_weight = create_seamless_weight((tile_height, tile_width), blend_width)
    
    print(f"Seamless blend width: {blend_width} pixels")
    
    # Create mosaic with advanced blending
    print("🎨 Creating seamless mosaic...")
    
    if is_color:
        mosaic = np.zeros((max_y, max_x, channels), dtype=np.float64)
        weight_sum = np.zeros((max_y, max_x), dtype=np.float64)
    else:
        mosaic = np.zeros((max_y, max_x), dtype=np.float64)
        weight_sum = np.zeros((max_y, max_x), dtype=np.float64)
    
    # Place tiles with seamless blending
    for i, (y, x) in enumerate(positions):
        if i >= len(images):
            break
        
        img = images[i].astype(np.float64)
        
        # Calculate placement region
        y_start = int(y)
        y_end = y_start + tile_height
        x_start = int(x)
        x_end = x_start + tile_width
        
        # Ensure bounds
        y_end = min(y_end, max_y)
        x_end = min(x_end, max_x)
        
        actual_h = y_end - y_start
        actual_w = x_end - x_start
        
        if actual_h <= 0 or actual_w <= 0:
            continue
        
        # Get regions
        if is_color:
            img_region = img[:actual_h, :actual_w, :]
            weight_region = tile_weight[:actual_h, :actual_w]
            
            # Blend each channel
            for c in range(channels):
                mosaic[y_start:y_end, x_start:x_end, c] += img_region[:,:,c] * weight_region
        else:
            img_region = img[:actual_h, :actual_w]
            weight_region = tile_weight[:actual_h, :actual_w]
            mosaic[y_start:y_end, x_start:x_end] += img_region * weight_region
        
        weight_sum[y_start:y_end, x_start:x_end] += weight_region
        
        if i % 10 == 0:
            print(f"Blended tile {i+1}/{num_images}")
    
    # Normalize
    print("🔧 Normalizing mosaic...")
    weight_sum[weight_sum == 0] = 1
    
    if is_color:
        for c in range(channels):
            mosaic[:,:,c] /= weight_sum
    else:
        mosaic /= weight_sum
    
    # Convert back to uint8
    mosaic = np.clip(mosaic, 0, 255).astype(np.uint8)
    
    # Apply final enhancement
    print("✨ Applying final enhancement...")
    
    if is_color:
        # Enhance contrast per channel
        for c in range(channels):
            mosaic[:,:,c] = exposure.equalize_adapthist(mosaic[:,:,c], clip_limit=0.01)
            mosaic[:,:,c] = (mosaic[:,:,c] * 255).astype(np.uint8)
    else:
        mosaic = exposure.equalize_adapthist(mosaic, clip_limit=0.01)
        mosaic = (mosaic * 255).astype(np.uint8)
    
    # Save results
    print("💾 Saving results...")
    
    # PNG for viewing
    png_output = os.path.join(folder_path, "HYBRID_mosaic_SEAMLESS.png")
    skimage.io.imsave(png_output, mosaic)
    print(f"PNG saved: {png_output}")
    
    # TIFF for analysis
    tiff_output = os.path.join(folder_path, "HYBRID_mosaic_SEAMLESS.tif")
    tifffile.imwrite(tiff_output, mosaic)
    print(f"TIFF saved: {tiff_output}")
    
    # OME-TIFF for ImageJ
    ome_output = os.path.join(folder_path, "HYBRID_mosaic_SEAMLESS.ome.tif")
    tifffile.imwrite(ome_output, mosaic, 
                    metadata={'axes': 'YX', 'PhysicalSizeX': 1.0, 'PhysicalSizeY': 1.0})
    print(f"OME-TIFF saved: {ome_output}")
    
    # Statistics
    print(f"\n📊 FINAL STATISTICS:")
    print(f"Mosaic size: {mosaic.shape[1]} x {mosaic.shape[0]} pixels")
    print(f"File size: ~{(mosaic.nbytes / 1024 / 1024):.1f} MB")
    print(f"Coverage: {np.count_nonzero(mosaic) / mosaic.size * 100:.1f}%")
    
    return mosaic

def main():
    folder_path = "D:/Stitch/bilinear_stitching_20250915_152529"
    
    if len(os.sys.argv) > 1:
        folder_path = os.sys.argv[1]
    
    if not os.path.exists(folder_path):
        print(f"Folder not found: {folder_path}")
        return
    
    try:
        result = create_hybrid_mosaic(folder_path)
        
        print("\n" + "=" * 60)
        print("🎉 HYBRID STITCHING COMPLETED!")
        print("=" * 60)
        print("✅ Grid positioning (accurate alignment)")
        print("✅ Exposure matching (consistent colors)")
        print("✅ Seamless blending (no visible boundaries)")
        print("✅ Distance transform weights (smooth transitions)")
        print("✅ Adaptive histogram equalization (enhanced contrast)")
        print("\n🎯 BEST OF BOTH WORLDS:")
        print("   📐 Accuracy of grid positioning")
        print("   🎨 Quality of advanced blending")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
