#!/usr/bin/env python3
"""
ASHLAR with relaxed parameters for difficult alignments
"""

import os
import glob
import numpy as np
import skimage.io
from ashlar import reg

def main():
    # Configuration
    input_folder = "D:/Stitch/bilinear_stitching_20250915_152529"  # Ganti dengan path Anda
    output_file = "D:/Stitch/bilinear_stitching_20250915_152529/stitched_relaxed.ome.tif"
    
    # Parameters - more relaxed for difficult alignments
    grid_width = 9
    grid_height = 4
    overlap = 0.4  # Increased overlap to 40%
    flip_y = True
    
    # Relaxed alignment parameters
    max_shift = 50  # Increased from default 15
    alpha = 0.1     # Increased from default 0.01 (more permissive)
    filter_sigma = 2.0  # Add some filtering
    
    print("ASHLAR with Relaxed Parameters")
    print("=" * 40)
    
    # Load files
    png_files = glob.glob(os.path.join(input_folder, "*.png"))
    png_files.sort()
    
    print(f"Found {len(png_files)} PNG files")
    print(f"Grid: {grid_width}x{grid_height}")
    print(f"Overlap: {overlap*100}%")
    print(f"Max shift: {max_shift} μm")
    print(f"Alpha: {alpha}")
    
    # Read first image to get dimensions
    first_img = skimage.io.imread(png_files[0])
    if first_img.ndim == 3:
        first_img = first_img[:,:,0]  # Take first channel if RGB
    
    tile_height, tile_width = first_img.shape
    num_images = len(png_files)
    
    print(f"Image dimensions: {tile_width}x{tile_height}")
    
    # Calculate tile positions with larger overlap
    positions = []
    for i in range(num_images):
        row = i // grid_width
        col = i % grid_width
        
        # Snake pattern: reverse odd rows
        if row % 2 == 1:
            col = grid_width - 1 - col
        
        pos_y = row * tile_height * (1 - overlap)
        pos_x = col * tile_width * (1 - overlap)
        positions.append([pos_y, pos_x])
    
    positions = np.array(positions)
    
    # Apply flip Y if needed
    if flip_y:
        positions = positions * [-1, 1]
        print("Applied Y flip")
    
    print("Calculated tile positions with increased overlap")
    
    # Create metadata class
    class SimpleMetadata:
        def __init__(self, positions, tile_size, dtype, num_images):
            self._positions = positions
            self._tile_size = np.array(tile_size)
            self._dtype = dtype
            self._num_images = num_images
            self._num_channels = 1
            self._pixel_size = 1.0
        
        @property
        def num_images(self):
            return self._num_images
        
        @property
        def num_channels(self):
            return self._num_channels
        
        @property
        def pixel_size(self):
            return self._pixel_size
        
        @property
        def pixel_dtype(self):
            return self._dtype
        
        @property
        def positions(self):
            return self._positions
        
        @property
        def size(self):
            return self._tile_size
        
        @property
        def origin(self):
            return self.positions.min(axis=0)
        
        @property
        def centers(self):
            return self.positions + self.size / 2
        
        def tile_position(self, i):
            return self._positions[i]
        
        def tile_size(self, i):
            return self._tile_size
    
    # Create reader class
    class SimpleReader:
        def __init__(self, file_list, metadata):
            self.file_list = file_list
            self.metadata = metadata
        
        def read(self, series, c):
            img = skimage.io.imread(self.file_list[series])
            if img.ndim == 3:
                img = img[:,:,0]  # Take first channel if RGB
            return img
    
    # Create metadata and reader
    metadata = SimpleMetadata(positions, (tile_height, tile_width), first_img.dtype, num_images)
    reader = SimpleReader(png_files, metadata)
    
    print("Created reader and metadata")
    
    # Run edge alignment with relaxed parameters
    print("Starting edge alignment with relaxed parameters...")
    edge_aligner = reg.EdgeAligner(
        reader, 
        verbose=True,
        max_shift=max_shift,
        alpha=alpha,
        filter_sigma=filter_sigma
    )
    edge_aligner.run()
    
    print("Edge alignment completed")
    print(f"Mosaic shape: {edge_aligner.mosaic_shape}")
    
    # Create mosaic
    print("Creating mosaic...")
    mosaic = reg.Mosaic(edge_aligner, edge_aligner.mosaic_shape, verbose=True)
    
    # Write output
    print(f"Writing output to {output_file}...")
    
    if output_file.endswith('.ome.tif'):
        writer = reg.PyramidWriter([mosaic], output_file, verbose=True)
    else:
        writer = reg.TiffListWriter([mosaic], output_file, verbose=True)
    
    writer.run()
    
    print("=" * 40)
    print("✓ Stitching completed with relaxed parameters!")
    print(f"✓ Output saved to: {output_file}")

if __name__ == "__main__":
    main()
