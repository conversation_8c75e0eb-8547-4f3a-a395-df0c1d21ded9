#!/usr/bin/env python3
"""
Analyze bilinear stitching files to determine correct grid and parameters
"""

import os
import glob
import re
import numpy as np

def analyze_files(folder_path):
    print("Analyzing bilinear stitching files...")
    print("=" * 50)
    
    # Get all PNG files
    png_files = glob.glob(os.path.join(folder_path, "*.png"))
    png_files.sort()
    
    print(f"Found {len(png_files)} PNG files")
    
    if not png_files:
        print("No PNG files found!")
        return
    
    # Analyze file names to extract coordinates
    coordinates = []
    series_numbers = []
    
    for file in png_files:
        basename = os.path.basename(file)
        print(f"File: {basename}")
        
        # Extract series number (p000, p001, etc.)
        series_match = re.search(r'p(\d+)', basename)
        if series_match:
            series_numbers.append(int(series_match.group(1)))
        
        # Extract X, Y coordinates
        x_match = re.search(r'X([\d.]+)', basename)
        y_match = re.search(r'Y([\d.]+)', basename)
        
        if x_match and y_match:
            x_coord = float(x_match.group(1))
            y_coord = float(y_match.group(1))
            coordinates.append((x_coord, y_coord))
            print(f"  Series: {series_match.group(1) if series_match else 'N/A'}, X: {x_coord}, Y: {y_coord}")
    
    print("\n" + "=" * 50)
    print("ANALYSIS RESULTS:")
    print("=" * 50)
    
    # Analyze series numbers
    if series_numbers:
        series_numbers.sort()
        print(f"Series range: {min(series_numbers)} to {max(series_numbers)}")
        print(f"Total series: {len(series_numbers)}")
        
        # Check for missing series
        expected = list(range(min(series_numbers), max(series_numbers) + 1))
        missing = set(expected) - set(series_numbers)
        if missing:
            print(f"Missing series: {sorted(missing)}")
        else:
            print("✓ All series numbers present")
    
    # Analyze coordinates
    if coordinates:
        coordinates = np.array(coordinates)
        x_coords = coordinates[:, 0]
        y_coords = coordinates[:, 1]
        
        print(f"\nCoordinate ranges:")
        print(f"X: {x_coords.min():.3f} to {x_coords.max():.3f}")
        print(f"Y: {y_coords.min():.3f} to {y_coords.max():.3f}")
        
        # Find unique X and Y positions
        unique_x = np.unique(np.round(x_coords, 3))
        unique_y = np.unique(np.round(y_coords, 3))
        
        print(f"\nUnique X positions: {len(unique_x)}")
        print(f"X positions: {unique_x}")
        print(f"\nUnique Y positions: {len(unique_y)}")
        print(f"Y positions: {unique_y}")
        
        # Determine grid dimensions
        grid_width = len(unique_x)
        grid_height = len(unique_y)
        
        print(f"\n🎯 RECOMMENDED GRID: {grid_width}x{grid_height}")
        print(f"Total expected tiles: {grid_width * grid_height}")
        
        if grid_width * grid_height == len(png_files):
            print("✅ Grid matches number of files!")
        else:
            print("❌ Grid doesn't match number of files")
            print("   This might indicate missing files or irregular grid")
        
        # Calculate spacing
        if len(unique_x) > 1:
            x_spacing = np.diff(unique_x).mean()
            print(f"\nAverage X spacing: {x_spacing:.3f}")
        
        if len(unique_y) > 1:
            y_spacing = np.diff(unique_y).mean()
            print(f"Average Y spacing: {y_spacing:.3f}")
        
        # Determine scanning direction
        print(f"\n📋 SCANNING ANALYSIS:")
        
        # Check if X changes faster (horizontal scanning) or Y changes faster (vertical scanning)
        if len(coordinates) >= 2:
            first_coord = coordinates[0]
            second_coord = coordinates[1]
            
            x_diff = abs(second_coord[0] - first_coord[0])
            y_diff = abs(second_coord[1] - first_coord[1])
            
            if x_diff > y_diff:
                print("Direction: HORIZONTAL (X changes first)")
                print("Layout suggestion: Use 'horizontal' direction")
            else:
                print("Direction: VERTICAL (Y changes first)")
                print("Layout suggestion: Use 'vertical' direction")
        
        # Check for snake pattern
        print(f"\n🐍 SNAKE PATTERN ANALYSIS:")
        if len(coordinates) >= grid_width * 2:  # Need at least 2 rows
            # Check if second row is reversed
            first_row_x = coordinates[:grid_width, 0]
            second_row_x = coordinates[grid_width:grid_width*2, 0]
            
            first_row_sorted = np.sort(first_row_x)
            second_row_sorted = np.sort(second_row_x)
            
            if np.allclose(first_row_sorted, second_row_sorted):
                # Same X positions, check if order is reversed
                if np.allclose(first_row_x, second_row_x[::-1]):
                    print("✅ SNAKE pattern detected!")
                    print("Layout suggestion: Use 'snake' layout")
                else:
                    print("❓ RASTER pattern detected")
                    print("Layout suggestion: Use 'raster' layout")
            else:
                print("❓ Irregular pattern")
        
        # Overlap estimation
        print(f"\n📏 OVERLAP ESTIMATION:")
        print("Assuming standard microscope tile size of ~2048x2048 pixels")
        print("With your coordinate spacing:")
        
        if len(unique_x) > 1 and len(unique_y) > 1:
            # Estimate overlap based on coordinate spacing
            # This is rough estimation - actual overlap depends on pixel size
            estimated_overlap_x = max(0, 1 - (x_spacing / 2.0))  # Very rough estimate
            estimated_overlap_y = max(0, 1 - (y_spacing / 2.0))  # Very rough estimate
            
            print(f"Estimated X overlap: ~{estimated_overlap_x:.1f}")
            print(f"Estimated Y overlap: ~{estimated_overlap_y:.1f}")
            print("Recommended overlap: 0.1 to 0.3 (10% to 30%)")

def main():
    # Default path - change this to your folder
    folder_path = "D:/Stitch/bilinear_stitching_20250915_152529"
    
    if len(os.sys.argv) > 1:
        folder_path = os.sys.argv[1]
    
    if not os.path.exists(folder_path):
        print(f"Folder not found: {folder_path}")
        print("Usage: python analyze_files.py [folder_path]")
        return
    
    analyze_files(folder_path)
    
    print("\n" + "=" * 50)
    print("RECOMMENDED ASHLAR PARAMETERS:")
    print("=" * 50)
    print("Based on the analysis above, try these parameters:")
    print("- Grid: Use the recommended grid dimensions")
    print("- Direction: Use the suggested direction (horizontal/vertical)")
    print("- Layout: Use the suggested layout (snake/raster)")
    print("- Overlap: Start with 0.2 (20%) and adjust if needed")
    print("- Flip Y: Try both True and False to see which gives better results")

if __name__ == "__main__":
    main()
